<?php
/**
 * Plugin Name: AccuFlow - Google Sheets Integration
 * Plugin URI:  https://accuflow.com/
 * Description: Tự động phân phối tài khoản từ Google Sheets hoặc API tùy chỉnh qua email khi đơn hàng WooCommerce hoàn thành, với trang quản lý tập trung và tùy chọn nhập liệu frontend.
 * Version:     2.5.0
 * Author:      AccuFlow Team
 * Author URI:  https://accuflow.com
 * License:     GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: accuflow
 */

// Ngăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Định nghĩa đường dẫn đến thư mục plugin
define( 'ACCUFLOW_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'ACCUFLOW_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'ACCUFLOW_VENDOR_DIR', ACCUFLOW_PLUGIN_DIR . 'vendor/' );
define( 'ACCUFLOW_VERSION', '2.5.0' ); // Giữ phiên bản 2.5.0 như yêu cầu

// Kiểm tra xem thư viện Google API Client đã được tải chưa
if ( file_exists( ACCUFLOW_VENDOR_DIR . 'autoload.php' ) ) {
    require_once ACCUFLOW_VENDOR_DIR . 'autoload.php';
} else {
    // Thông báo lỗi nếu thư viện không tìm thấy (quan trọng!)
    add_action( 'admin_notices', function() {
        echo '<div class="notice notice-error"><p><strong>AccuFlow - Google Sheets Integration:</strong> Thư viện Google API Client không tìm thấy. Vui lòng chạy <code>composer install</code> trong thư mục plugin hoặc tải thư mục <code>vendor</code> lên.</p></div>';
    });
    return; // Dừng kích hoạt plugin nếu không có thư viện
}

/**
 * Tải các file cần thiết
 */
require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-accuflow-config.php';
require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-accuflow-utils.php';
require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-accuflow-google-api.php';
require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-accuflow-email-template.php';
require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-accuflow-order-fulfillment.php';

/**
 * Hàm khởi tạo plugin
 */
function accuflow_run() {
    $config = new AccuFlow_Config();
    $utils = new AccuFlow_Utils( $config );
    $google_api = new AccuFlow_Google_API( $config, $utils );
    $email_template = new AccuFlow_Email_Template( $config );
    $order_fulfillment = new AccuFlow_Order_Fulfillment( $config, $google_api, $email_template );
    $order_fulfillment->init(); // Khởi tạo các hooks

    if ( is_admin() ) {
        require_once ACCUFLOW_PLUGIN_DIR . 'admin/class-accuflow-admin.php';
        $admin = new AccuFlow_Admin( $config, $google_api, $utils, $order_fulfillment );
        $admin->init(); // Khởi tạo các hooks admin

        // Set global variable for use in included files
        global $accuflow_admin;
        $accuflow_admin = $admin;
    }
}
add_action( 'plugins_loaded', 'accuflow_run' );