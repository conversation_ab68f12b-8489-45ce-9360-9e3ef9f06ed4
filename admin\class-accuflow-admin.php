<?php
/**
 * AccuFlow - Google Sheets Integration: <PERSON><PERSON><PERSON> quản lý giao diện Admin
 *
 * @package AccuFlow
 * @subpackage Admin
 * @since 2.5.0
 */

// Ngăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

if ( ! class_exists( 'AccuFlow_Admin' ) ) {
    /**
     * Lớp AccuFlow_Admin xử lý tất cả các chức năng và giao diện trong khu vực admin.
     */
    class AccuFlow_Admin {

        private $config;
        private $google_api;
        private $utils;
        private $order_fulfillment;

        public function __construct( AccuFlow_Config $config, AccuFlow_Google_API $google_api, AccuFlow_Utils $utils, AccuFlow_Order_Fulfillment $order_fulfillment ) {
            $this->config            = $config;
            $this->google_api        = $google_api;
            $this->utils             = $utils;
            $this->order_fulfillment = $order_fulfillment;
        }

        public function init() {
            add_action( 'admin_init', [$this, 'redirect_wc_email_settings_tab'] );
            add_action( 'admin_menu', [$this, 'add_admin_menu'] );
            add_action( 'admin_enqueue_scripts', [$this, 'enqueue_admin_scripts'] );
            add_action( 'wp_ajax_accuflow_test_connection_ajax', [$this, 'handle_test_connection_ajax'] );
            add_action( 'wp_ajax_accuflow_detailed_diagnosis_ajax', [$this, 'handle_detailed_diagnosis_ajax'] );
            add_action( 'wp_ajax_accuflow_download_cacert_ajax', [$this, 'handle_download_cacert_ajax'] );
            add_action( 'wp_ajax_accuflow_test_product_link_ajax', [$this, 'handle_test_product_link_ajax'] );
            add_action( 'wp_ajax_accuflow_write_connection_log_ajax', [$this, 'handle_write_connection_log_ajax'] );
            add_action( 'wp_ajax_accuflow_test_api_call_ajax', [$this, 'handle_test_api_call_ajax'] );
            add_action( 'wp_ajax_accuflow_email_preview_ajax', [$this, 'handle_email_preview_ajax'] );
            add_action( 'wp_ajax_accuflow_send_test_email_ajax', [$this, 'handle_send_test_email_ajax'] );
            add_action( 'wp_ajax_accuflow_auto_save_fulfillment_method', [$this, 'handle_auto_save_fulfillment_method_ajax'] );
            add_action( 'wp_ajax_accuflow_get_product_list_ajax', [$this, 'handle_get_product_list_ajax'] );
        }

        public function add_admin_menu() {
            add_submenu_page(
                'woocommerce',
                'AccuFlow - Quản lý Tài khoản',
                'AccuFlow',
                'manage_options',
                'accuflow-sheets-integration',
                [$this, 'admin_page_content']
            );
        }

        public function redirect_wc_email_settings_tab() {
            if ( ! $this->config->get_setting('enable_wc_email_management', false) ) {
                return;
            }
            global $pagenow;
            if ( 'admin.php' === $pagenow && isset( $_GET['page'] ) && $_GET['page'] === 'wc-settings' && isset( $_GET['tab'] ) && $_GET['tab'] === 'email' ) {
                wp_redirect( admin_url( 'admin.php?page=accuflow-sheets-integration&tab=wc-emails' ) );
                exit;
            }
        }

        public function enqueue_admin_scripts() {
            if ( isset( $_GET['page'] ) && $_GET['page'] === 'accuflow-sheets-integration' ) {
                wp_enqueue_style( 'wp-color-picker' );
                wp_enqueue_style( 'accuflow-admin-styles', ACCUFLOW_PLUGIN_URL . 'admin/css/admin-styles.css', [], ACCUFLOW_VERSION );
                wp_enqueue_script( 'accuflow-admin-scripts', ACCUFLOW_PLUGIN_URL . 'admin/js/admin-scripts.js', ['jquery', 'wp-color-picker'], ACCUFLOW_VERSION, true );
                wp_localize_script( 'accuflow-admin-scripts', 'accuflow_ajax_object', [
                    'ajax_url'                            => admin_url( 'admin-ajax.php' ),
                    'test_connection_nonce'               => wp_create_nonce( 'accuflow_test_connection_nonce' ),
                    'detailed_diagnosis_nonce'            => wp_create_nonce( 'accuflow_detailed_diagnosis_nonce' ),
                    'download_cacert_nonce'               => wp_create_nonce( 'accuflow_download_cacert_nonce' ),
                    'test_product_link_nonce'             => wp_create_nonce( 'accuflow_test_product_link_nonce' ),
                    'write_connection_log_nonce'          => wp_create_nonce( 'accuflow_write_connection_log_nonce' ),
                    'test_api_call_nonce'                 => wp_create_nonce( 'accuflow_test_api_call_nonce' ),
                    'email_preview_nonce'                 => wp_create_nonce( 'accuflow_email_preview_nonce' ),
                    'send_test_email_nonce'               => wp_create_nonce( 'accuflow_send_test_email_nonce' ),
                    'save_product_links_nonce'            => wp_create_nonce( 'accuflow_save_product_links_nonce' ),
                    'get_product_list_nonce'              => wp_create_nonce( 'accuflow_get_product_list_nonce' ),
                    'current_s_query'                     => isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '',
                    'current_paged'                       => isset($_GET['paged']) ? absint($_GET['paged']) : 1,
                ]);
            }
        }
        
        public function admin_page_content() {
            $current_settings = $this->config->get_config();
            $message = '';
            $message_type = '';



            // Check for success message from URL
            if (isset($_GET['saved']) && $_GET['saved'] === '1') {
                $message = 'Cài đặt đã được lưu thành công!';
                $message_type = 'success';
            } elseif (isset($_GET['message']) && isset($_GET['message_type'])) {
                $message = sanitize_text_field($_GET['message']);
                $message_type = sanitize_text_field($_GET['message_type']);
            }



            // Xử lý lưu Cài đặt Chung
            if ( isset( $_POST['accuflow_save_settings'] ) && check_admin_referer( 'accuflow_save_settings_nonce' ) ) {
                $new_settings = [
                    'spreadsheet_id'                   => sanitize_text_field( $_POST['spreadsheet_id'] ),
                    'service_account_project_id'       => sanitize_text_field( $_POST['service_account_project_id'] ?? '' ),
                    'service_account_private_key_id'   => sanitize_text_field( $_POST['service_account_private_key_id'] ?? '' ),
                    'service_account_private_key'      => sanitize_textarea_field( $_POST['service_account_private_key'] ?? '' ),
                    'service_account_client_email'     => sanitize_email( $_POST['service_account_client_email'] ?? '' ),
                    'service_account_client_id'        => sanitize_text_field( $_POST['service_account_client_id'] ?? '' ),
                    'service_account_client_x509_cert_url' => esc_url_raw( $_POST['service_account_client_x509_cert_url'] ?? '' ),
                    'status_available'                 => sanitize_text_field( $_POST['status_available'] ),
                    'status_sold'                      => sanitize_text_field( $_POST['status_sold'] ),
                    'shop_name'                        => sanitize_text_field( $_POST['shop_name'] ),
                    'shop_logo_url'                    => esc_url_raw( $_POST['shop_logo_url'] ),
                    'support_url'                      => esc_url_raw( $_POST['support_url'] ),
                    'support_text'                     => sanitize_text_field( $_POST['support_text'] ),
                    'disable_ssl_verify'               => isset( $_POST['disable_ssl_verify'] ),
                    'test_log_sheet_name'              => sanitize_text_field( $_POST['test_log_sheet_name'] ?? 'AccuFlow Test Log' ),
                    'enable_variation_matching'        => isset( $_POST['enable_variation_matching'] ),
                ];

                // Handle column configuration
                $column_config_method = sanitize_text_field($_POST['column_config_method'] ?? 'individual');
                $column_mapping_string = sanitize_text_field($_POST['column_mapping_string'] ?? '');
                $column_indices_string = sanitize_text_field($_POST['column_indices_string'] ?? '');

                $columns = [];

                if ($column_config_method === 'string' && !empty($column_mapping_string) && !empty($column_indices_string)) {
                    // Parse dynamic string format
                    $column_names = array_map('trim', explode('|', $column_mapping_string));
                    $column_indices = array_map('trim', explode('|', $column_indices_string));

                    // Build columns array dynamically
                    for ($i = 0; $i < min(count($column_names), count($column_indices)); $i++) {
                        $name = sanitize_key($column_names[$i]);
                        $index = absint($column_indices[$i]);
                        if (!empty($name)) {
                            $columns[$name] = $index;
                        }
                    }

                    $new_settings['column_mapping_string'] = $column_mapping_string;
                } else {
                    // Individual column configuration
                    if (isset($_POST['columns']) && is_array($_POST['columns'])) {
                        foreach ( $_POST['columns'] as $key => $value ) {
                            $columns[sanitize_key($key)] = absint( $value );
                        }
                    }
                    $new_settings['column_mapping_string'] = '';
                }

                $new_settings['columns'] = $columns;

                $this->config->save_settings( $new_settings );

                // Redirect to show success message and preserve tab
                $current_tab = isset($_POST['current_tab']) ? sanitize_text_field($_POST['current_tab']) : 'settings';
                $redirect_url = admin_url('admin.php?page=accuflow-sheets-integration&tab=' . $current_tab . '&saved=1');
                wp_redirect($redirect_url);
                exit;
            }

            // Xử lý lưu Liên kết Sản phẩm
            if ( isset( $_POST['accuflow_save_product_links'] ) && wp_verify_nonce( $_POST['_wpnonce'], 'accuflow_save_product_links_nonce' ) ) {
                $product_ids = isset( $_POST['product_ids'] ) ? array_map( 'absint', $_POST['product_ids'] ) : [];

                foreach ( $product_ids as $post_id ) {
                    $fulfillment_method = sanitize_text_field( $_POST['fulfillment_method'][ $post_id ] ?? 'none' );
                    update_post_meta( $post_id, $current_settings['product_fulfillment_method_meta_key'], $fulfillment_method );
                    
                    $duration_days = sanitize_text_field( $_POST['duration_days'][ $post_id ] ?? '' );
                    update_post_meta( $post_id, $current_settings['product_duration_days_meta_key'], $duration_days );

                    // Reset meta keys for other methods to clear old data
                    delete_post_meta( $post_id, $current_settings['product_sheet_tab_meta_key'] );
                    delete_post_meta( $post_id, $current_settings['product_api_endpoint_meta_key'] );
                    delete_post_meta( $post_id, $current_settings['product_api_input_fields_meta_key'] );
                    delete_post_meta( $post_id, $current_settings['product_api_custom_link_regex_meta_key'] );
                    delete_post_meta( $post_id, $current_settings['product_api_json_filter_meta_key'] );
                    delete_post_meta( $post_id, $current_settings['product_api_note_filter_meta_key'] );
                    delete_post_meta( $post_id, $current_settings['product_api_show_frontend_meta_key'] );

                    if ( $fulfillment_method === 'google_sheet' ) {
                        $current_tab_gid = isset($_POST['tab_gid'][$post_id]) ? sanitize_text_field( $_POST['tab_gid'][ $post_id ] ) : '';
                        update_post_meta( $post_id, $current_settings['product_sheet_tab_meta_key'], $current_tab_gid );
                    } elseif ( $fulfillment_method === 'api_call' ) {
                        $api_endpoint_url = isset($_POST['api_endpoint_url'][$post_id]) ? sanitize_text_field( $_POST['api_endpoint_url'][ $post_id ] ) : '';
                        $api_input_fields = isset( $_POST['api_input_fields'][ $post_id ] ) ? array_map( 'sanitize_text_field', $_POST['api_input_fields'][ $post_id ] ) : [];
                        $api_custom_link_regex = isset($_POST['api_custom_link_regex'][$post_id]) ? sanitize_text_field( $_POST['api_custom_link_regex'][ $post_id ] ) : '';
                        $api_json_filter = isset($_POST['api_json_filter'][$post_id]) ? sanitize_textarea_field( $_POST['api_json_filter'][ $post_id ] ) : '';
                        $api_note_filter = isset($_POST['api_note_filter'][$post_id]) ? sanitize_textarea_field( $_POST['api_note_filter'][ $post_id ] ) : '';
                        $api_show_frontend = isset($_POST['api_show_frontend'][$post_id]) ? '1' : '';
                        $api_placeholders = isset($_POST['api_placeholders'][$post_id]) ? array_map('sanitize_text_field', $_POST['api_placeholders'][$post_id]) : [];

                        // Validate API endpoint URL contains required placeholders
                        if (!empty($api_endpoint_url)) {
                            error_log('AccuFlow Debug: Saving API endpoint URL: ' . $api_endpoint_url);

                            // Check if URL contains placeholders
                            if (strpos($api_endpoint_url, '{') === false || strpos($api_endpoint_url, '}') === false) {
                                error_log('AccuFlow Debug: Warning - API URL may be missing placeholders: ' . $api_endpoint_url);
                            }
                        }

                        update_post_meta( $post_id, $current_settings['product_api_endpoint_meta_key'], $api_endpoint_url );
                        update_post_meta( $post_id, $current_settings['product_api_input_fields_meta_key'], $api_input_fields );
                        update_post_meta( $post_id, $current_settings['product_api_custom_link_regex_meta_key'], $api_custom_link_regex );
                        update_post_meta( $post_id, $current_settings['product_api_json_filter_meta_key'], $api_json_filter );
                        update_post_meta( $post_id, $current_settings['product_api_note_filter_meta_key'], $api_note_filter );
                        update_post_meta( $post_id, $current_settings['product_api_show_frontend_meta_key'], $api_show_frontend );
                        update_post_meta( $post_id, $current_settings['product_api_placeholders_meta_key'], $api_placeholders );
                    }
                }
                $message = 'Liên kết sản phẩm đã được lưu thành công!';
                $message_type = 'success';
                
                // Redirect to keep current tab
                $current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'product-links';
                $redirect_url = admin_url('admin.php?page=accuflow-sheets-integration&tab=' . $current_tab);
                if (!empty($message)) {
                    $redirect_url .= '&message=' . urlencode($message) . '&message_type=' . $message_type;
                }
                wp_redirect($redirect_url);
                exit;
            }
            
            // Xử lý lưu cài đặt Email WooCommerce
            if ( isset( $_POST['accuflow_save_wc_emails_settings'] ) && check_admin_referer( 'accuflow_save_wc_emails_settings_nonce' ) ) {
                $new_settings = [];
                $new_settings['enable_wc_email_management'] = isset( $_POST['enable_wc_email_management'] );

                $all_wc_emails = WC_Emails::instance()->get_emails();
                $new_wc_email_custom_configs = [];

                foreach ( $all_wc_emails as $email_id => $email_object ) {
                    $posted_email_config = $_POST['wc_email_custom_configs'][$email_id] ?? [];
                    $enabled = isset( $posted_email_config['enabled'] );
                    if ( $enabled ) {
                        $new_wc_email_custom_configs[$email_id] = [ 'enabled' => true ];
                    }
                }
                $new_settings['wc_email_custom_configs'] = $new_wc_email_custom_configs;
                
                $this->config->save_settings( $new_settings );

                // Redirect to show success message and preserve tab
                $current_tab = isset($_POST['current_tab']) ? sanitize_text_field($_POST['current_tab']) : 'wc-emails';
                $redirect_url = admin_url('admin.php?page=accuflow-sheets-integration&tab=' . $current_tab . '&saved=1');
                wp_redirect($redirect_url);
                exit;
            }

            // Tải lại cấu hình sau khi có thể đã lưu
            $current_settings = $this->config->get_config();

            $paged = isset( $_GET['paged'] ) ? absint( $_GET['paged'] ) : 1;
            $s = isset( $_GET['s'] ) ? sanitize_text_field( $_GET['s'] ) : '';
            $posts_per_page = 10;
            $args = ['post_type' => 'product', 'post_status' => 'publish', 'posts_per_page' => $posts_per_page, 'paged' => $paged, 's' => $s];
            $product_query = new WP_Query( $args );
            
            $total_pages = $product_query->max_num_pages;

            ?>
            <div class="wrap accuflow-admin-page">
                <h1><span class="dashicons dashicons-forms"></span> AccuFlow - Quản lý Tài khoản tự động</h1>
                <?php if ( $message ) : ?>
                    <div class="notice notice-<?php echo esc_attr( $message_type ); ?> is-dismissible"><p><?php echo esc_html( $message ); ?></p></div>
                <?php endif; ?>
                <?php
                // Determine current tab from URL parameter
                $current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'settings';
                $valid_tabs = ['settings', 'product-links', 'wc-emails'];
                if (!in_array($current_tab, $valid_tabs)) {
                    $current_tab = 'settings';
                }
                ?>
                <div class="accuflow-tabs">
                    <button class="accuflow-tab-button <?php echo $current_tab === 'settings' ? 'active' : ''; ?>" data-tab="settings">Cài đặt Chung</button>
                    <button class="accuflow-tab-button <?php echo $current_tab === 'product-links' ? 'active' : ''; ?>" data-tab="product-links">Liên kết Sản phẩm</button>
                    <button class="accuflow-tab-button <?php echo $current_tab === 'wc-emails' ? 'active' : ''; ?>" data-tab="wc-emails">Quản lý Email WooCommerce</button>
                </div>
                <div id="accuflow-tab-settings" class="accuflow-tab-content <?php echo $current_tab === 'settings' ? 'active' : ''; ?>">
                    <?php include ACCUFLOW_PLUGIN_DIR . 'admin/partials/admin-display-settings.php'; ?>
                </div>
                <div id="accuflow-tab-product-links" class="accuflow-tab-content <?php echo $current_tab === 'product-links' ? 'active' : ''; ?>">
                    <?php
                    // Prepare variables for product links display
                    $s = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';
                    $paged = isset($_GET['paged']) ? absint($_GET['paged']) : 1;

                    // Query products for display
                    $args = [
                        'post_type' => 'product',
                        'post_status' => 'publish',
                        'posts_per_page' => 20,
                        'paged' => $paged,
                    ];

                    if (!empty($s)) {
                        $args['s'] = $s;
                    }

                    $product_query = new WP_Query($args);
                    $total_pages = $product_query->max_num_pages;

                    // Pass instances to the included file
                    $config_instance = $this->config;
                    $admin_instance = $this;

                    include ACCUFLOW_PLUGIN_DIR . 'admin/partials/admin-display-product-links.php';
                    ?>
                </div>
                <div id="accuflow-tab-wc-emails" class="accuflow-tab-content <?php echo $current_tab === 'wc-emails' ? 'active' : ''; ?>">
                    <?php include ACCUFLOW_PLUGIN_DIR . 'admin/partials/admin-display-woocommerce-emails.php'; ?>
                </div>
            </div>
            <?php
        }

        public function render_product_list_with_variations( $product_query ) {
            if ( $product_query->have_posts() ) {
                while ( $product_query->have_posts() ) {
                    $product_query->the_post();
                    $product = wc_get_product( get_the_ID() );
                    if ( ! $product ) continue;
                    
                    $this->render_single_product_row( $product );

                    if ( $product->is_type('variable') ) {
                        $variations = $product->get_children();
                        foreach ( $variations as $variation_id ) {
                            $variation_product = wc_get_product( $variation_id );
                            if ( $variation_product && $variation_product->exists() ) {
                                $this->render_single_product_row( $variation_product, true );
                            }
                        }
                    }
                }
                wp_reset_postdata();
            } else {
                echo '<tr><td colspan="6">Không tìm thấy sản phẩm WooCommerce nào.</td></tr>';
            }
        }
        
        private function render_single_product_row( $product, $is_variation = false ) {
            $config = $this->config->get_config();
            $post_id = $product->get_id();
            
            $current_fulfillment_method = get_post_meta( $post_id, $config['product_fulfillment_method_meta_key'], true ) ?: 'none';
            $current_duration_days = get_post_meta( $post_id, $config['product_duration_days_meta_key'], true );
            
            $current_tab_gid = get_post_meta( $post_id, $config['product_sheet_tab_meta_key'], true );
            $current_api_endpoint_url = get_post_meta( $post_id, $config['product_api_endpoint_meta_key'], true );
            $current_api_input_fields = get_post_meta( $post_id, $config['product_api_input_fields_meta_key'], true );
            if ( ! is_array( $current_api_input_fields ) ) $current_api_input_fields = [];
            $current_api_custom_link_regex = get_post_meta( $post_id, $config['product_api_custom_link_regex_meta_key'], true );
            $current_api_note_filter = get_post_meta( $post_id, $config['product_api_note_filter_meta_key'], true );
            $current_api_show_frontend = get_post_meta( $post_id, $config['product_api_show_frontend_meta_key'], true );

            $product_name = $product->get_name();
            if ( $is_variation ) {
                $attribute_summary = wc_get_formatted_variation( $product, true, false, true );
                $product_name = '&nbsp;&nbsp;&nbsp; &rarr; ' . ($attribute_summary ?: 'Biến thể #' . $post_id);
            }
            ?>
            <tr class="<?php echo $is_variation ? 'accuflow-variation-row' : 'accuflow-parent-row'; ?>">
                <td>
                    <input type="hidden" name="product_ids[]" value="<?php echo esc_attr( $post_id ); ?>">
                    <a href="<?php echo esc_url( get_edit_post_link( $post_id ) ); ?>" target="_blank"><?php echo wp_kses_post( $product_name ); ?></a>
                </td>
                <td><?php echo esc_html( $product->get_sku() ); ?></td>
                <td>
                    <select name="fulfillment_method[<?php echo esc_attr( $post_id ); ?>]" class="accuflow-fulfillment-method-select" data-product-id="<?php echo esc_attr( $post_id ); ?>">
                        <option value="none" <?php selected( $current_fulfillment_method, 'none' ); ?>>Không cấu hình</option>
                        <option value="google_sheet" <?php selected( $current_fulfillment_method, 'google_sheet' ); ?>>Google Sheet</option>
                        <option value="api_call" <?php selected( $current_fulfillment_method, 'api_call' ); ?>>API Call</option>
                    </select>
                </td>
                <td>
                    <div class="accuflow-duration-config">
                        <label for="duration_days_<?php echo esc_attr( $post_id ); ?>" class="screen-reader-text">Thời hạn (ngày)</label>
                        <input type="number" name="duration_days[<?php echo esc_attr( $post_id ); ?>]" id="duration_days_<?php echo esc_attr( $post_id ); ?>" value="<?php echo esc_attr( $current_duration_days ); ?>" class="small-text" placeholder="VD: 30" style="width: 70px;">
                    </div>
                </td>
                <td>
                    <div class="accuflow-fulfillment-config accuflow-sheet-config" id="sheet-config-<?php echo esc_attr($post_id); ?>" style="display: <?php echo ( $current_fulfillment_method === 'google_sheet' ) ? 'block' : 'none'; ?>;">
                        <label for="tab_gid_<?php echo esc_attr( $post_id ); ?>">Google Sheet Tab GID:</label>
                        <input type="text" name="tab_gid[<?php echo esc_attr( $post_id ); ?>]" id="tab_gid_<?php echo esc_attr( $post_id ); ?>" value="<?php echo esc_attr( $current_tab_gid ); ?>" class="accuflow-sheets-tab-gid regular-text" placeholder="GID của tab">
                    </div>
                    <div class="accuflow-fulfillment-config accuflow-api-config" id="api-config-<?php echo esc_attr($post_id); ?>" style="display: <?php echo ( $current_fulfillment_method === 'api_call' ) ? 'block' : 'none'; ?>;">
                        <div style="padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; text-align: center;">
                            <p style="margin: 0 0 15px 0; color: #6c757d;">
                                <strong>🔧 API Call Configuration</strong><br>
                                <small>Click để cấu hình API endpoint, input fields và JSON filter</small>
                            </p>
                            <button type="button" class="button button-secondary accuflow-api-settings-btn" data-product-id="<?php echo esc_attr($post_id); ?>" style="background: #007cba; color: white; border: none; padding: 8px 20px; border-radius: 4px;">
                                ⚙️ Cài đặt API
                            </button>

                            <!-- Hidden inputs to store data -->
                            <input type="hidden" name="api_endpoint_url[<?php echo esc_attr( $post_id ); ?>]" value="<?php echo esc_attr( $current_api_endpoint_url ); ?>" class="accuflow-api-endpoint-url">
                            <input type="hidden" name="api_json_filter[<?php echo esc_attr( $post_id ); ?>]" value="<?php echo esc_attr( get_post_meta($post_id, $config['product_api_json_filter_meta_key'], true) ); ?>" class="accuflow-api-json-filter">
                            <input type="hidden" name="api_note_filter[<?php echo esc_attr( $post_id ); ?>]" value="<?php echo esc_attr( $current_api_note_filter ); ?>" class="accuflow-api-note-filter">
                            <input type="hidden" name="api_custom_link_regex[<?php echo esc_attr( $post_id ); ?>]" value="<?php echo esc_attr( $current_api_custom_link_regex ); ?>" class="accuflow-api-custom-link-regex">
                            <input type="hidden" name="api_show_frontend[<?php echo esc_attr( $post_id ); ?>]" value="<?php echo esc_attr( $current_api_show_frontend ); ?>" class="accuflow-api-show-frontend">

                            <!-- Hidden inputs for input fields -->
                            <?php foreach ($current_api_input_fields as $field): ?>
                            <input type="hidden" name="api_input_fields[<?php echo esc_attr( $post_id ); ?>][]" value="<?php echo esc_attr($field); ?>">
                            <?php endforeach; ?>

                            <!-- Hidden inputs for placeholders -->
                            <?php
                            $current_placeholders = get_post_meta($post_id, $config['product_api_placeholders_meta_key'], true);
                            if (is_array($current_placeholders)):
                                foreach ($current_placeholders as $field => $placeholder):
                            ?>
                            <input type="hidden" name="api_placeholders[<?php echo esc_attr( $post_id ); ?>][<?php echo esc_attr($field); ?>]" value="<?php echo esc_attr($placeholder); ?>">
                            <?php
                                endforeach;
                            endif;
                            ?>
                        </div>




                    </div>
                </td>
                <td>
                    <div class="accuflow-sheet-config-buttons" id="sheet-buttons-<?php echo esc_attr($post_id); ?>" style="display: <?php echo ( $current_fulfillment_method === 'google_sheet' ) ? 'block' : 'none'; ?>;">
                        <button type="button" class="button button-small accuflow-sheets-test-link" data-product-id="<?php echo esc_attr( $post_id ); ?>">Test Đọc</button><br>
                        <button type="button" class="button button-small accuflow-sheets-write-log-link" data-product-id="<?php echo esc_attr( $post_id ); ?>" data-product-sku="<?php echo esc_attr( $product->get_sku() ); ?>">Ghi log</button>
                    </div>
                    <div class="accuflow-api-config-buttons" id="api-buttons-<?php echo esc_attr($post_id); ?>" style="display: <?php echo ( $current_fulfillment_method === 'api_call' ) ? 'block' : 'none'; ?>;">
                        <button type="button" class="button button-small accuflow-sheets-test-api-call" data-product-id="<?php echo esc_attr( $post_id ); ?>">Test API</button>
                    </div>
                    <span class="accuflow-sheets-test-result" id="test-result-<?php echo esc_attr( $post_id ); ?>"></span>
                </td>
            </tr>
            <?php
        }

        public function handle_test_connection_ajax() {
            check_ajax_referer( 'accuflow_test_connection_nonce', 'security' );
            $spreadsheetId = $this->config->get_setting( 'spreadsheet_id' );
            $service = $this->google_api->get_sheets_service();
            if ( $service && ! empty( $spreadsheetId ) ) {
                try {
                    $spreadsheet_metadata = $service->spreadsheets->get( $spreadsheetId );
                    $sheet_title = $spreadsheet_metadata->getProperties()->getTitle();
                    wp_send_json_success( '✅ Kết nối Google Sheet thành công! Tên Sheet: ' . esc_html( $sheet_title ) );
                } catch ( Exception $e ) {
                    wp_send_json_error( '❌ Lỗi kết nối Google Sheet: ' . esc_html( $e->getMessage() ) );
                }
            } else {
                wp_send_json_error( '❌ Không thể khởi tạo dịch vụ Google Sheets. Vui lòng kiểm tra Service Account Credentials và Sheet ID.' );
            }
        }
        
        public function handle_detailed_diagnosis_ajax() {
             check_ajax_referer( 'accuflow_detailed_diagnosis_nonce', 'security' );
            $diagnosis_results = $this->utils->check_php_environment();
            wp_send_json_success( implode( '<br>', $diagnosis_results ) );
        }
        
        public function handle_download_cacert_ajax() {
            check_ajax_referer( 'accuflow_download_cacert_nonce', 'security' );
            if ( $this->utils->download_cacert() ) {
                wp_send_json_success( 'Tệp `cacert.pem` đã được tải xuống và cập nhật thành công!' );
            } else {
                wp_send_json_error( 'Lỗi khi tải xuống hoặc ghi tệp `cacert.pem`.' );
            }
        }

        public function handle_test_product_link_ajax() {
            check_ajax_referer( 'accuflow_test_product_link_nonce', 'security' );
            $product_id = absint( $_POST['product_id'] );
            $tab_gid = sanitize_text_field( $_POST['tab_gid'] );
            if ( empty( $tab_gid ) ) {
                wp_send_json_error( 'Vui lòng điền GID Tab.' );
            }
            $spreadsheetId = $this->config->get_setting( 'spreadsheet_id' );
            $sheet_name_from_gid = $this->google_api->get_sheet_name_from_gid( $spreadsheetId, $tab_gid );
            if ( ! $sheet_name_from_gid ) {
                wp_send_json_error( '❌ Không tìm thấy tab với GID "' . esc_html( $tab_gid ) . '".' );
            }
            $account_details = $this->google_api->find_first_available_account_in_gid( $tab_gid );
            if ( $account_details ) {
                wp_send_json_success( '✅ Đọc thành công tài khoản "Available" đầu tiên: ' . esc_html( $account_details['username'] ) );
            } else {
                wp_send_json_error( '❌ Không tìm thấy tài khoản "Available" nào trong tab.' );
            }
        }

        public function handle_write_connection_log_ajax() {
            check_ajax_referer( 'accuflow_write_connection_log_nonce', 'security' );
            $product_id = absint( $_POST['product_id'] );
            $tab_gid = sanitize_text_field( $_POST['tab_gid'] );
            $product_sku = sanitize_text_field( $_POST['product_sku'] ?? '' );

            $spreadsheetId = $this->config->get_setting( 'spreadsheet_id' );
            $sheet_name_to_log = $this->google_api->get_sheet_name_from_gid( $spreadsheetId, $tab_gid );
            if ( ! $sheet_name_to_log ) {
                wp_send_json_error( '❌ Không tìm thấy tab với GID "' . esc_html( $tab_gid ) . '".' );
            }

            $message_to_log = 'Đã kết nối đúng tab: ' . $sheet_name_to_log . ' (GID: ' . $tab_gid . ')';
            $written = $this->google_api->write_test_log( $sheet_name_to_log, $product_sku, 'Connection Log', 'Success', $message_to_log );

            if ( $written ) {
                wp_send_json_success( '✅ Đã ghi log kết nối thành công vào tab "' . esc_html( $sheet_name_to_log ) . '".' );
            } else {
                wp_send_json_error( '❌ Lỗi khi ghi log kết nối.' );
            }
        }

        public function handle_test_api_call_ajax() {
            check_ajax_referer( 'accuflow_test_api_call_nonce', 'security' );
            $api_endpoint_url = isset($_POST['api_endpoint_url']) ? sanitize_text_field($_POST['api_endpoint_url']) : '';
            $api_json_filter = isset($_POST['api_json_filter']) ? sanitize_textarea_field($_POST['api_json_filter']) : '';
            
            if (empty($api_endpoint_url)) {
                wp_send_json_error('Vui lòng điền API Endpoint URL.');
            }

            // Test với dữ liệu mẫu
            $test_data = [
                'email' => '<EMAIL>',
                'password' => 'testpass123',
                'custom_link' => 'https://example.com',
                'note' => 'Test note'
            ];

            $final_url = $api_endpoint_url;
            foreach ($test_data as $key => $value) {
                $final_url = str_replace('{' . $key . '}', urlencode($value), $final_url);
            }

            $response = wp_remote_get($final_url, array('timeout' => 30));

            if (is_wp_error($response)) {
                wp_send_json_error('❌ Lỗi API: ' . $response->get_error_message());
            }

            $body = wp_remote_retrieve_body($response);
            $http_code = wp_remote_retrieve_response_code($response);
            $json_data = json_decode($body, true);

            if ($http_code == 200) {
                $result_message = "✅ <strong>API Test thành công!</strong><br/>";
                $result_message .= "<strong>HTTP Code:</strong> {$http_code}<br/>";
                
                if ($json_data && !empty($api_json_filter)) {
                    // Use Order Fulfillment class to filter JSON
                    $order_fulfillment = new AccuFlow_Order_Fulfillment($this->config, $this->google_api, $this->utils);
                    $filtered = $order_fulfillment->test_filter_json_response($json_data, $api_json_filter);

                    if ($filtered !== null) {
                        $result_message .= "<strong>🎯 Kết quả lọc:</strong><br/>";
                        if (is_string($filtered)) {
                            $result_message .= "<pre style='background:#e8f5e8;padding:15px;border-radius:5px;border-left:4px solid #28a745;'>" . esc_html($filtered) . "</pre>";
                        } else {
                            $result_message .= "<pre style='background:#e8f5e8;padding:15px;border-radius:5px;'>" . esc_html(print_r($filtered, true)) . "</pre>";
                        }
                    } else {
                        $result_message .= "<strong>⚠️ JSONPath không tìm thấy dữ liệu</strong><br/>";
                    }
                }
                
                $result_message .= "<details style='margin-top:10px;'>";
                $result_message .= "<summary style='cursor:pointer;font-weight:bold;color:#0073aa;'>📄 Xem Response đầy đủ</summary>";
                $result_message .= "<pre style='background:#f8f9fa;padding:15px;border-radius:5px;max-height:300px;overflow-y:auto;margin-top:10px;'>" . htmlspecialchars(json_encode($json_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
                $result_message .= "</details>";
                wp_send_json_success($result_message);
            } else {
                wp_send_json_error("❌ <strong>Lỗi HTTP {$http_code}:</strong><br/>" . htmlspecialchars(substr($body, 0, 200)));
            }
        }
        
        public function handle_email_preview_ajax() {
            check_ajax_referer( 'accuflow_email_preview_nonce', 'security' );
            wp_send_json_error('Chức năng xem trước email chưa được triển khai đầy đủ.');
        }

        public function handle_send_test_email_ajax() {
            check_ajax_referer( 'accuflow_send_test_email_nonce', 'security' );
            wp_send_json_error('Chức năng gửi email test chưa được triển khai đầy đủ.');
        }

        /**
         * Format kết quả multiple filters cho hiển thị chuyên nghiệp
         */
        private function format_multiple_filter_result($filtered_result, $product_name = '') {
            if (!is_array($filtered_result)) {
                return $filtered_result;
            }
            
            $formatted = [];
            $formatted[] = "📋 Kết quả API cho sản phẩm \"" . $product_name . "\":";
            $formatted[] = "";
            
            foreach ($filtered_result as $label => $value) {
                if (is_array($value) || is_object($value)) {
                    $formatted[] = "🔹 " . ucfirst($label) . ": " . json_encode($value, JSON_UNESCAPED_UNICODE);
                } else {
                    // Format đặc biệt cho các loại dữ liệu khác nhau
                    if (filter_var($value, FILTER_VALIDATE_URL)) {
                        $formatted[] = "🔗 " . ucfirst($label) . ": " . $value;
                    } elseif (is_numeric($value)) {
                        $formatted[] = "🔢 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'password') !== false || strpos(strtolower($label), 'pass') !== false) {
                        $formatted[] = "🔐 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'username') !== false || strpos(strtolower($label), 'user') !== false) {
                        $formatted[] = "👤 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'email') !== false) {
                        $formatted[] = "📧 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'date') !== false || strpos(strtolower($label), 'time') !== false || strpos(strtolower($label), 'expire') !== false) {
                        $formatted[] = "📅 " . ucfirst($label) . ": " . $value;
                    } else {
                        $formatted[] = "📝 " . ucfirst($label) . ": " . $value;
                    }
                }
            }
            
            $formatted[] = "";
            $formatted[] = "⏰ Thời gian xử lý: " . current_time('d/m/Y H:i:s');
            
            return implode("\n", $formatted);
        }

        /**
         * Format kết quả cho admin note (hiển thị ngắn gọn)
         */
        private function format_admin_note_result($filtered_result, $product_name, $order_id) {
            if (!is_array($filtered_result)) {
                return "✅ API thành công cho đơn hàng #{$order_id} ({$product_name}): " . $filtered_result;
            }
            
            $summary = [];
            foreach ($filtered_result as $label => $value) {
                if (is_string($value) && strlen($value) > 50) {
                    $summary[] = ucfirst($label) . ": " . substr($value, 0, 47) . "...";
                } else {
                    $summary[] = ucfirst($label) . ": " . $value;
                }
            }
            
            return "✅ API thành công cho đơn hàng #{$order_id} ({$product_name}). " . implode(", ", $summary);
        }

        /**
         * Format kết quả cho customer note (hiển thị đầy đủ và đẹp)
         */
        private function format_customer_note_result($filtered_result, $product_name) {
            if (!is_array($filtered_result)) {
                return "Kết quả cho sản phẩm \"{$product_name}\":\n{$filtered_result}";
            }
            
            $formatted = [];
            $formatted[] = "🎯 Thông tin tài khoản cho sản phẩm \"{$product_name}\"";
            $formatted[] = str_repeat("─", 50);
            
            foreach ($filtered_result as $label => $value) {
                if (is_array($value) || is_object($value)) {
                    $formatted[] = "📋 " . ucfirst($label) . ":";
                    $formatted[] = "   " . json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                } else {
                    // Format với icon phù hợp
                    if (filter_var($value, FILTER_VALIDATE_URL)) {
                        $formatted[] = "🔗 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'password') !== false || strpos(strtolower($label), 'pass') !== false) {
                        $formatted[] = "🔐 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'username') !== false || strpos(strtolower($label), 'user') !== false) {
                        $formatted[] = "👤 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'email') !== false) {
                        $formatted[] = "📧 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'download') !== false || strpos(strtolower($label), 'link') !== false) {
                        $formatted[] = "⬇️ " . ucfirst($label) . ": " . $value;
                    } else {
                        $formatted[] = "📝 " . ucfirst($label) . ": " . $value;
                    }
                }
            }
            
            $formatted[] = str_repeat("─", 50);
            $formatted[] = "⏰ Cập nhật lúc: " . current_time('H:i\\h \\n\\g\\à\\y d/m/Y');
            
            return implode("\n", $formatted);
        }

        public function handle_get_product_list_ajax() {
            check_ajax_referer( 'accuflow_get_product_list_nonce', 'security' );

            // Check if WooCommerce is active
            if (!class_exists('WooCommerce')) {
                wp_send_json_error('WooCommerce không được kích hoạt');
                return;
            }

            $search_query = isset($_POST['s']) ? sanitize_text_field($_POST['s']) : '';
            $paged = isset($_POST['paged']) ? absint($_POST['paged']) : 1;

            // Query products
            $args = [
                'post_type' => 'product',
                'post_status' => 'publish',
                'posts_per_page' => 20,
                'paged' => $paged,
            ];

            if (!empty($search_query)) {
                $args['s'] = $search_query;
            }

            $product_query = new WP_Query($args);



            // Generate HTML for product rows
            ob_start();
            if ($product_query->have_posts()) {
                while ($product_query->have_posts()) {
                    $product_query->the_post();
                    $product = wc_get_product(get_the_ID());
                    if (!$product) continue;

                    $this->render_single_product_row($product);

                    if ($product->is_type('variable')) {
                        $variations = $product->get_children();
                        foreach ($variations as $variation_id) {
                            $variation_product = wc_get_product($variation_id);
                            if ($variation_product && $variation_product->exists()) {
                                $this->render_single_product_row($variation_product, true);
                            }
                        }
                    }
                }
                wp_reset_postdata();
            } else {
                echo '<tr><td colspan="6">Không tìm thấy sản phẩm WooCommerce nào.</td></tr>';
            }
            $rows_html = ob_get_clean();

            // Generate pagination
            $pagination_html = '';
            if ($product_query->max_num_pages > 1) {
                ob_start();
                $big = 999999999;
                echo paginate_links([
                    'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
                    'format' => '?paged=%#%',
                    'current' => $paged,
                    'total' => $product_query->max_num_pages,
                    'prev_text' => '&laquo; Trước',
                    'next_text' => 'Sau &raquo;',
                    'type' => 'list',
                    'add_args' => false,
                    'add_fragment' => '',
                    'before_page_number' => '<span class="accuflow-pagination-link" data-page="',
                    'after_page_number' => '">',
                ]);
                $pagination_html = ob_get_clean();
            }

            wp_send_json_success([
                'rows' => $rows_html,
                'pagination' => $pagination_html
            ]);
        }

        public function handle_auto_save_fulfillment_method_ajax() {
            check_ajax_referer( 'accuflow_save_product_links_nonce', 'security' );

            $product_id = absint( $_POST['product_id'] );
            $fulfillment_method = sanitize_text_field( $_POST['fulfillment_method'] );

            $current_settings = $this->config->get_config();

            if ( $product_id && in_array( $fulfillment_method, ['none', 'google_sheet', 'api_call'] ) ) {
                update_post_meta( $product_id, $current_settings['product_fulfillment_method_meta_key'], $fulfillment_method );
                wp_send_json_success( 'Đã lưu phương thức fulfillment' );
            } else {
                wp_send_json_error( 'Dữ liệu không hợp lệ' );
            }
        }

        /**
         * Helper method to use order fulfillment's filter_json_response
         */
        private function filter_json_response($json_data, $json_filter) {
            // Use reflection to call private method from order fulfillment class
            $reflection = new ReflectionClass($this->order_fulfillment);
            $method = $reflection->getMethod('filter_json_response');
            $method->setAccessible(true);
            return $method->invoke($this->order_fulfillment, $json_data, $json_filter);
        }
    }
}
