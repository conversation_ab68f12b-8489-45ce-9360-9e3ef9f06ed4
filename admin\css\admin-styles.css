/* General <PERSON><PERSON> Page Styling */

/* Modal Styles */
.accuflow-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.accuflow-modal.show {
    opacity: 1;
    visibility: visible;
}

.accuflow-modal.show .accuflow-modal-content {
    transform: scale(1);
    opacity: 1;
}

.accuflow-modal-content {
    background-color: #fff;
    margin: 2% auto;
    padding: 0;
    border: none;
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transform: scale(0.9);
    opacity: 0;
    transition: all 0.3s ease;
}

.accuflow-modal-header {
    padding: 20px 28px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    position: relative;
    flex-shrink: 0;
}

.modal-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
}

.modal-title-section {
    flex: 1;
}

.accuflow-modal-header h2 {
    margin: 0 0 6px 0;
    color: white;
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 13px;
    font-weight: 400;
    line-height: 1.4;
}

/* New Modal Styles for API Configuration */
.accuflow-modal-body {
    overflow-y: auto;
    max-height: calc(90vh - 160px);
}

.config-section {
    padding: 20px 28px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    gap: 14px;
    align-items: flex-start;
}

.config-section:last-child {
    border-bottom: none;
}

.section-icon {
    font-size: 18px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 6px;
    color: white;
    flex-shrink: 0;
    margin-top: 1px;
}

.section-content {
    flex: 1;
}

.section-content h3 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.section-description {
    margin: 0 0 16px 0;
    color: #7f8c8d;
    font-size: 12px;
    line-height: 1.4;
}

.input-group {
    margin-bottom: 16px;
}

.input-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: #34495e;
    font-size: 12px;
}

.input-group input,
.input-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.2s ease;
    font-family: inherit;
    box-sizing: border-box;
}

.input-group textarea {
    resize: vertical;
    min-height: 70px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.input-group input:focus,
.input-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.help-text {
    margin-top: 6px;
    font-size: 11px;
    color: #6c757d;
}

.help-label {
    font-weight: 500;
    color: #495057;
    margin-right: 6px;
}

.variable-tag {
    display: inline-block;
    background: #e3f2fd;
    color: #1976d2;
    padding: 1px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 10px;
    margin-right: 4px;
    margin-bottom: 3px;
}

.example-tags {
    margin: 8px 0;
}

.help-note {
    margin-top: 8px;
    font-style: italic;
}

.field-selection {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 20px;
}

.field-option {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    transition: all 0.2s ease;
    border: 1px solid #e9ecef;
}

.field-option:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.field-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin: 0;
}

.field-checkbox input[type="checkbox"] {
    width: auto;
    margin-right: 12px;
    transform: scale(1.2);
}

.field-label {
    font-weight: 500;
    color: #495057;
}

.placeholder-configs h4 {
    margin: 20px 0 12px 0;
    font-size: 15px;
    font-weight: 600;
    color: #2c3e50;
    border-top: 1px solid #e9ecef;
    padding-top: 20px;
}

.placeholder-field {
    margin-bottom: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #667eea;
}

.test-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.test-button {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.test-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.test-icon {
    font-size: 16px;
}

.test-result {
    margin-top: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #6c757d;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    white-space: pre-wrap;
}

.toggle-option {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.toggle-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin: 0;
}

.toggle-checkbox input[type="checkbox"] {
    width: auto;
    margin-right: 12px;
    transform: scale(1.2);
}

.toggle-label {
    font-weight: 500;
    color: #495057;
}

.button-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.button-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.button-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.button-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .accuflow-modal-content {
        width: 95%;
        margin: 1% auto;
        max-height: 95vh;
    }

    .config-section {
        padding: 16px 20px;
        flex-direction: column;
        gap: 12px;
    }

    .section-icon {
        align-self: flex-start;
    }

    .field-selection {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .accuflow-modal-header {
        padding: 20px 24px;
    }

    .accuflow-modal-header h2 {
        font-size: 24px;
    }

    .accuflow-modal-footer {
        padding: 16px 20px;
        flex-direction: column;
        gap: 8px;
    }

    .button-primary,
    .button-secondary {
        width: 100%;
        justify-content: center;
    }
}

/* Google Sheets Column Configuration */
.sheet-column-config {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #ddd;
}

.config-method-selector {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.config-method-selector label {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
}

.config-method-selector input[type="radio"] {
    margin-right: 8px;
}

.config-section {
    margin-top: 15px;
}

.column-input-group {
    background: white;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    transition: all 0.2s ease;
}

.column-input-group:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 4px rgba(0, 115, 170, 0.1);
}

.column-input-group label {
    color: #23282d;
    font-size: 13px;
}

.column-input-group input {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 13px;
}

#string-config-preview {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
}

#preview-content {
    margin-top: 8px;
}

.preview-mapping {
    display: flex;
    align-items: center;
    margin: 4px 0;
    padding: 4px 8px;
    background: white;
    border-radius: 4px;
    border-left: 3px solid #0073aa;
}

.preview-column {
    font-weight: bold;
    color: #0073aa;
    min-width: 40px;
}

.preview-arrow {
    margin: 0 10px;
    color: #666;
}

.preview-field {
    color: #333;
}

/* Enhanced String Configuration */
.string-config-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 15px;
}

@media (max-width: 768px) {
    .string-config-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

.config-input-section,
.column-indices-section {
    background: white;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.config-label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #2c3e50;
}

.input-with-helper {
    position: relative;
}

.helper-buttons {
    display: flex;
    gap: 8px;
    margin-top: 10px;
}

.helper-buttons .button {
    font-size: 12px;
    padding: 4px 12px;
    height: auto;
    line-height: 1.4;
}

#add-column-btn {
    background: #e8f5e8;
    border-color: #4caf50;
    color: #2e7d32;
}

#add-column-btn:hover {
    background: #c8e6c9;
    border-color: #388e3c;
}

#reset-default-btn {
    background: #fff3e0;
    border-color: #ff9800;
    color: #e65100;
}

#reset-default-btn:hover {
    background: #ffe0b2;
    border-color: #f57c00;
}

.accuflow-modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.accuflow-modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
}

.close-icon {
    color: white;
    font-size: 16px;
    font-weight: bold;
    line-height: 1;
}
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    font-size: 32px;
    font-weight: bold;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.accuflow-modal-close:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-50%) scale(1.1);
}

.accuflow-modal-body {
    padding: 30px;
    flex: 1;
    overflow-y: auto;
    background: #f8f9fa;
}

.accuflow-modal-footer {
    padding: 25px 30px;
    background: #ffffff;
    border-top: 1px solid #dee2e6;
    text-align: right;
    flex-shrink: 0;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
}

.accuflow-modal-footer .button {
    margin-left: 15px;
    padding: 12px 25px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.accuflow-modal-footer .button-primary {
    background: linear-gradient(135deg, #0073aa 0%, #005a87 100%);
    border: none;
    color: white;
}

.accuflow-modal-footer .button-primary:hover {
    background: linear-gradient(135deg, #005a87 0%, #004666 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,115,170,0.3);
}

/* API Section Styles */
.api-section {
    margin-bottom: 30px;
    padding: 25px;
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.api-section h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    border-bottom: 3px solid #0073aa;
    padding-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
}

.api-section h4 {
    margin: 20px 0 15px 0;
    color: #495057;
    font-size: 16px;
    font-weight: 500;
}

.api-section label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.api-section input[type="text"],
.api-section textarea {
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    padding: 12px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.api-section input[type="text"]:focus,
.api-section textarea:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 3px rgba(0,115,170,0.1);
    outline: none;
}

.api-section .description {
    color: #6c757d;
    font-size: 13px;
    margin-top: 8px;
    line-height: 1.4;
}

.input-fields-checkboxes {
    margin-bottom: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.input-fields-checkboxes label {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.input-fields-checkboxes label:hover {
    background: #e9ecef;
    border-color: #0073aa;
}

.input-fields-checkboxes input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

.input-fields-checkboxes input[type="checkbox"]:checked + span {
    color: #0073aa;
}

.placeholder-configs {
    margin-top: 25px;
}

.placeholder-configs .placeholder-field {
    margin: 15px 0;
    padding: 20px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.placeholder-configs .placeholder-field.active {
    border-color: #0073aa;
    background: #ffffff;
}

.placeholder-configs .placeholder-field label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.placeholder-configs .placeholder-field input {
    width: 100%;
    margin-bottom: 8px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    padding: 10px;
    font-size: 14px;
}

.placeholder-configs .placeholder-field input:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 3px rgba(0,115,170,0.1);
    outline: none;
}

/* Test API Button */
#modal-test-api {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
}

#modal-test-api:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40,167,69,0.3);
}

#modal-test-result {
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.5;
}
.accuflow-admin-page {
    margin-top: 20px;
    max-width: 1200px; /* Limit width for better readability */
}

.accuflow-admin-page h1 {
    color: #2c3e50; /* Dark blue-gray */
    font-size: 2.2em;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
}

.accuflow-admin-page h1 .dashicons {
    font-size: 1.2em;
    margin-right: 10px;
    color: #3498db; /* Blue */
}

/* Card Styling */
.accuflow-card {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    overflow: hidden; /* Clear floats for nested elements */
}

.accuflow-card-header {
    background-color: #f7f7f7;
    padding: 20px 25px;
    border-bottom: 1px solid #e0e0e0;
}

.accuflow-card-header h2 {
    margin: 0;
    font-size: 1.6em;
    color: #34495e; /* Darker blue-gray */
    display: flex;
    align-items: center;
}

.accuflow-card-header h2 .dashicons {
    margin-right: 10px;
    color: #3498db;
    font-size: 1.1em;
}

.accuflow-card-header .description {
    margin-top: 10px;
    font-size: 0.95em;
    color: #7f8c8d; /* Gray */
}

.accuflow-card-body {
    padding: 25px;
}

/* Form Table Enhancements */
.accuflow-card .form-table th {
    width: 25%; /* Adjust width for labels */
    padding-right: 20px;
    vertical-align: top;
}

.accuflow-card .form-table td {
    padding-bottom: 20px;
}

/* Input Fields */
.accuflow-card input[type="text"],
.accuflow-card input[type="email"],
.accuflow-card input[type="url"],
.accuflow-card input[type="number"],
.accuflow-card textarea.large-text,
.accuflow-card select {
    width: 100%;
    max-width: 500px;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
}

.accuflow-card textarea.code {
    font-family: monospace;
    background-color: #f8f8f8;
    border-color: #ccc;
}

.accuflow-card .description {
    font-size: 0.85em;
    color: #888;
    margin-top: 5px;
    line-height: 1.4;
}

/* Buttons */
.accuflow-submit-buttons .button {
    margin-right: 10px;
}

.accuflow-submit-buttons .button .dashicons {
    margin-right: 5px;
}

.button-info {
    background-color: #2980b9 !important;
    color: #fff !important;
    border-color: #2980b9 !important;
}
.button-info:hover {
    background-color: #3498db !important;
    border-color: #3498db !important;
}


/* JSON Converter & Diagnosis Box */
.accuflow-diagnosis-box {
    background-color: #fdf6e3; /* Light yellow for info */
    border: 1px solid #f9da92;
    border-radius: 6px;
    padding: 15px 20px;
    margin-bottom: 25px;
    position: relative; /* For close button positioning */
}
.accuflow-diagnosis-box h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #c09853; /* Darker yellow */
    font-size: 1.3em;
    display: flex;
    align-items: center;
}
.accuflow-diagnosis-box h3 .dashicons {
    margin-right: 8px;
    color: #f39c12; /* Orange-yellow */
}
.accuflow-diagnosis-box pre {
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 250px; /* Limit height for scroll */
    overflow-y: auto;
    background-color: #fff;
    border: 1px solid #eee;
    padding: 10px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.9em;
}
.accuflow-diagnosis-box .button-close-diagnosis {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    box-shadow: none;
    color: #888;
    cursor: pointer;
    padding: 5px;
}
.accuflow-diagnosis-box .button-close-diagnosis:hover {
    color: #333;
}
.accuflow-diagnosis-box .button-close-diagnosis .dashicons {
    font-size: 1.2em;
    height: 20px;
    width: 20px;
}

/* Test Results */
.test-success {
    color: #27ae60; /* Green */
    font-weight: bold;
    display: block; /* Ensure it takes full width for better visibility */
    margin-top: 5px;
}

.test-error {
    color: #c0392b; /* Red */
    font-weight: bold;
    display: block;
    margin-top: 5px;
}

.spinner {
    vertical-align: middle;
    margin-right: 5px;
}

/* Column Settings Layout */
.accuflow-column-settings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); /* Responsive grid */
    gap: 15px;
    margin-top: 10px;
    margin-bottom: 10px;
}

.accuflow-column-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}
.accuflow-column-item label {
    font-weight: 600;
    margin-bottom: 5px;
}
.accuflow-column-item input[type="number"] {
    width: 80px; /* Fixed width for number inputs */
    text-align: center;
}

/* Product Links Table */
.accuflow-tab-content .wp-list-table {
    margin-top: 20px;
}

.accuflow-tab-content .wp-list-table th,
.accuflow-tab-content .wp-list-table td {
    vertical-align: middle;
    padding: 12px 10px;
}

.accuflow-fulfillment-method {
    min-width: 150px;
}

.accuflow-fulfillment-config {
    margin-top: 10px;
    border-left: 3px solid #3498db;
    padding-left: 10px;
    background-color: #ecf0f1; /* Light gray background for config blocks */
    padding: 10px;
    border-radius: 4px;
}
.accuflow-fulfillment-config label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
}
.accuflow-fulfillment-config input[type="text"],
.accuflow-fulfillment-config input[type="url"] {
    max-width: 100%; /* Ensure inputs within config don't overflow */
}
.accuflow-fulfillment-config p.description {
    margin-top: 5px;
    margin-bottom: 5px;
}

.accuflow-fulfillment-config input[type="checkbox"] {
    margin-right: 5px;
}
.accuflow-api-custom-link-regex-field {
    margin-top: 10px;
    border-top: 1px solid #bdc3c7;
    padding-top: 10px;
}

.accuflow-sheet-config-buttons,
.accuflow-api-config-buttons {
    margin-top: 10px;
}

.accuflow-sheet-config-buttons .button,
.accuflow-api-config-buttons .button {
    margin-bottom: 5px;
    width: auto;
    min-width: 120px;
}

/* Tab Navigation */
.accuflow-tabs {
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
    display: flex; /* Use flexbox for tabs */
    gap: 5px; /* Space between buttons */
}

.accuflow-tab-button {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-bottom: none;
    padding: 10px 20px;
    cursor: pointer;
    font-weight: bold;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    transition: all 0.2s ease-in-out;
    color: #555;
    text-decoration: none; /* For potential future anchor link tabs */
    white-space: nowrap; /* Prevent wrapping */
}

.accuflow-tab-button:hover {
    background-color: #e5e5e5;
    color: #333;
}

.accuflow-tab-button.active {
    background-color: #ffffff;
    border-color: #c3c4c7;
    color: #007cba;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);
    z-index: 1; /* Bring active tab to front */
    position: relative;
    top: 1px; /* Overlap border */
}

.accuflow-tab-content {
    background-color: #ffffff;
    border: 1px solid #c3c4c7;
    border-top: none; /* Hide top border as tab button covers it */
    padding: 25px;
    border-radius: 0 0 8px 8px; /* Round bottom corners */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* Toggle Details (for JSON converter & troubleshooting) */
.accuflow-toggle-details {
    margin-bottom: 20px;
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 6px;
    overflow: hidden;
}

.accuflow-toggle-summary {
    background-color: #eaf2f7;
    padding: 15px 20px;
    font-size: 1.2em;
    font-weight: 600;
    cursor: pointer;
    color: #2c3e50;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
}

.accuflow-toggle-summary .dashicons {
    margin-right: 10px;
    color: #3498db;
    transition: transform 0.2s ease-in-out;
}

.accuflow-toggle-details[open] .accuflow-toggle-summary .dashicons {
    transform: rotate(90deg); /* Rotate icon when open */
}

.accuflow-toggle-content {
    padding: 20px;
    background-color: #ffffff; /* Inner content background */
}

.accuflow-actions-row {
    margin-top: 15px;
    display: flex;
    gap: 10px;
}

/* Pagination links within the table nav */
.tablenav-pages .pagination-links .button {
    background-color: #f0f0f0;
    border-color: #ddd;
    color: #555;
}
.tablenav-pages .pagination-links .button.current {
    background-color: #007cba;
    border-color: #007cba;
    color: #fff;
}
.tablenav-pages .pagination-links .button:hover {
    background-color: #e5e5e5;
    color: #333;
}
.tablenav-pages .pagination-links .button.current:hover {
    background-color: #006093;
    border-color: #006093;
}

/* Responsive Adjustments */
@media screen and (max-width: 782px) {
    .accuflow-card .form-table th,
    .accuflow-card .form-table td {
        display: block;
        width: 100%;
    }
    .accuflow-card .form-table th {
        padding-bottom: 0;
        text-align: left;
    }
    .accuflow-card .form-table td {
        padding-top: 5px;
    }
    .accuflow-column-settings {
        grid-template-columns: 1fr;
    }
    .accuflow-submit-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    .accuflow-submit-buttons .button {
        width: 100%;
        margin-right: 0;
        margin-bottom: 10px;
    }
    .accuflow-tabs {
        flex-wrap: wrap; /* Allow tabs to wrap on small screens */
    }
    .accuflow-tab-button {
        flex-grow: 1; /* Make buttons expand */
        text-align: center;
    }
}
/* WC Email Management Specific Styles */
.accuflow-wc-email-toggle summary {
    display: flex;
    align-items: center;
    gap: 10px;
}

.accuflow-wc-email-toggle summary strong {
    flex-grow: 1;
}

.accuflow-wc-email-toggle summary .dashicons {
    transition: transform 0.2s ease-in-out;
}

.accuflow-wc-email-toggle[open] summary .dashicons {
    transform: rotate(90deg);
}

.accuflow-wc-email-toggle .accuflow-toggle-content {
    background-color: #fcfcfc; /* Lighter background for inner content */
}

.accuflow-wc-email-toggle .warning-text {
    color: #e67e22; /* Orange for warning text */
    font-weight: bold;
    margin-top: 10px;
    margin-bottom: 15px;
}
/* admin/css/admin-styles.css */

/* --- CSS Variables for Easy Customization (Luxury Palette) --- */
:root {
    --accuflow-primary-color: #34495e; /* Dark Blue-Gray, main text, headings */
    --accuflow-secondary-color: #7f8c8d; /* Muted Gray, descriptions */
    --accuflow-accent-color: #c09e5a; /* Muted Gold, for highlights, buttons */
    --accuflow-light-bg: #f9fbfd; /* Very Light Blue-Gray, background */
    --accuflow-card-bg: #ffffff; /* White, card background */
    --accuflow-border-color: #e0e6ea; /* Light Gray-Blue, borders */
    --accuflow-hover-color: #2c3e50; /* Darker primary for hover */
    --accuflow-button-primary-bg: var(--accuflow-accent-color);
    --accuflow-button-primary-text: #ffffff;
    --accuflow-button-secondary-bg: var(--accuflow-border-color);
    --accuflow-button-secondary-text: var(--accuflow-primary-color);
    --accuflow-success-color: #2ecc71; /* Green for success */
    --accuflow-error-color: #e74c3c; /* Red for error */
    --accuflow-info-color: #3498db; /* Blue for info */
    --accuflow-warning-color: #f39c12; /* Orange for warning */
    --accuflow-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); /* Soft, subtle shadow */
    --accuflow-border-radius: 8px; /* Consistent rounded corners */
}

/* General Admin Page Styling */
.accuflow-admin-page {
    margin-top: 25px;
    max-width: 1200px;
    font-family: 'Inter', 'Helvetica Neue', Helvetica, Arial, sans-serif; /* Modern font stack */
    color: var(--accuflow-primary-color);
}

.accuflow-admin-page h1 {
    color: var(--accuflow-primary-color);
    font-size: 2.5em; /* Slightly larger */
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    font-weight: 700; /* Bolder */
    letter-spacing: -0.5px; /* Tighter letter spacing */
}

.accuflow-admin-page h1 .dashicons {
    font-size: 1.5em; /* Larger icon */
    margin-right: 15px;
    color: var(--accuflow-accent-color); /* Use accent color for main icon */
    transition: transform 0.3s ease-out; /* Smooth transition */
}
.accuflow-admin-page h1:hover .dashicons {
    transform: rotate(-5deg) scale(1.05); /* Slight effect on hover */
}


/* Card Styling */
.accuflow-card {
    background-color: var(--accuflow-card-bg);
    border: 1px solid var(--accuflow-border-color);
    border-radius: var(--accuflow-border-radius);
    box-shadow: var(--accuflow-box-shadow);
    margin-bottom: 30px;
    overflow: hidden;
    transition: all 0.3s ease-in-out; /* Smooth transitions for cards */
}
.accuflow-card:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12); /* More pronounced shadow on hover */
}

.accuflow-card-header {
    background-color: var(--accuflow-light-bg);
    padding: 20px 30px; /* More padding */
    border-bottom: 1px solid var(--accuflow-border-color);
}

.accuflow-card-header h2 {
    margin: 0;
    font-size: 1.8em; /* Slightly larger */
    color: var(--accuflow-primary-color);
    display: flex;
    align-items: center;
    font-weight: 600;
}

.accuflow-card-header h2 .dashicons {
    margin-right: 12px;
    color: var(--accuflow-info-color); /* Blue for secondary icons */
    font-size: 1.2em;
}

.accuflow-card-header .description {
    margin-top: 10px;
    font-size: 0.95em;
    color: var(--accuflow-secondary-color);
    line-height: 1.5;
}

.accuflow-card-body {
    padding: 30px; /* More padding */
}

/* Form Table Enhancements */
.accuflow-card .form-table th {
    width: 25%;
    padding-right: 25px; /* More padding */
    vertical-align: top;
    font-weight: 600;
    color: var(--accuflow-primary-color);
}

.accuflow-card .form-table td {
    padding-bottom: 25px; /* More padding */
}

/* Input Fields */
.accuflow-card input[type="text"],
.accuflow-card input[type="email"],
.accuflow-card input[type="url"],
.accuflow-card input[type="number"],
.accuflow-card textarea.large-text,
.accuflow-card select {
    width: 100%;
    max-width: 550px; /* Slightly wider */
    padding: 10px 15px; /* More padding */
    border: 1px solid var(--accuflow-border-color);
    border-radius: var(--accuflow-border-radius); /* Consistent rounded corners */
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.06); /* Softer inner shadow */
    transition: all 0.2s ease-in-out;
    background-color: var(--accuflow-card-bg);
    color: var(--accuflow-primary-color);
}
.accuflow-card input[type="text"]:focus,
.accuflow-card input[type="email"]:focus,
.accuflow-card input[type="url"]:focus,
.accuflow-card input[type="number"]:focus,
.accuflow-card textarea.large-text:focus,
.accuflow-card select:focus {
    border-color: var(--accuflow-accent-color); /* Highlight border on focus */
    box-shadow: 0 0 0 2px rgba(var(--accuflow-accent-color-rgb), 0.2); /* Soft focus ring */
    outline: none;
}
.accuflow-card textarea.code {
    font-family: 'JetBrains Mono', 'Fira Code', monospace; /* Nicer monospace font */
    background-color: var(--accuflow-light-bg);
    border-color: var(--accuflow-border-color);
}

.accuflow-card .description {
    font-size: 0.88em; /* Slightly larger */
    color: var(--accuflow-secondary-color);
    margin-top: 8px; /* More space */
    line-height: 1.5;
}

/* Buttons */
.accuflow-submit-buttons {
    display: flex;
    flex-wrap: wrap; /* Allow wrapping */
    gap: 15px; /* Consistent spacing */
    margin-top: 30px; /* More space */
}

.accuflow-submit-buttons .button {
    padding: 10px 20px; /* Adjust padding */
    border-radius: var(--accuflow-border-radius);
    font-weight: 600;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); /* Soft button shadow */
}

.accuflow-submit-buttons .button-primary {
    background-color: var(--accuflow-button-primary-bg) !important;
    color: var(--accuflow-button-primary-text) !important;
    border-color: var(--accuflow-button-primary-bg) !important;
}
.accuflow-submit-buttons .button-primary:hover {
    background-color: var(--accuflow-hover-color) !important; /* Darker on hover */
    border-color: var(--accuflow-hover-color) !important;
    transform: translateY(-2px); /* Slight lift */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.accuflow-submit-buttons .button-secondary {
    background-color: var(--accuflow-button-secondary-bg) !important;
    color: var(--accuflow-button-secondary-text) !important;
    border-color: var(--accuflow-button-secondary-bg) !important;
}
.accuflow-submit-buttons .button-secondary:hover {
    background-color: var(--accuflow-border-color) !important; /* Lighter on hover */
    border-color: var(--accuflow-border-color) !important;
    transform: translateY(-2px); /* Slight lift */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}


.accuflow-submit-buttons .button .dashicons {
    margin-right: 8px; /* More space for icons */
    font-size: 1.1em;
}

/* Specific button styles */
.button-info {
    background-color: var(--accuflow-info-color) !important;
    color: #fff !important;
    border-color: var(--accuflow-info-color) !important;
}
.button-info:hover {
    background-color: #2980b9 !important; /* Darker blue */
    border-color: #2980b9 !important;
}

/* JSON Converter & Diagnosis Box */
.accuflow-diagnosis-box {
    background-color: #fcf8e3; /* Softer yellow for info */
    border: 1px solid #faebcc;
    border-radius: var(--accuflow-border-radius);
    padding: 20px 25px; /* More padding */
    margin-bottom: 25px;
    position: relative;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    display: none; /* Hidden by default, JS handles display */
}
.accuflow-diagnosis-box h3 {
    margin-top: 0;
    margin-bottom: 12px;
    color: var(--accuflow-warning-color); /* Orange */
    font-size: 1.4em;
    display: flex;
    align-items: center;
    font-weight: 600;
}
.accuflow-diagnosis-box h3 .dashicons {
    margin-right: 10px;
    color: var(--accuflow-warning-color);
    font-size: 1.2em;
}
.accuflow-diagnosis-box pre {
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 280px; /* Slightly taller */
    overflow-y: auto;
    background-color: #fdfdfd;
    border: 1px solid #f0f0f0;
    padding: 15px;
    border-radius: var(--accuflow-border-radius);
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    font-size: 0.9em;
    line-height: 1.6;
}
.accuflow-diagnosis-box .button-close-diagnosis {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    box-shadow: none;
    color: var(--accuflow-secondary-color);
    cursor: pointer;
    padding: 5px;
    font-size: 1.2em;
    transition: all 0.2s ease-in-out;
}
.accuflow-diagnosis-box .button-close-diagnosis:hover {
    color: var(--accuflow-primary-color);
    transform: rotate(90deg) scale(1.1);
}


/* Test Results */
.test-success {
    color: var(--accuflow-success-color);
    font-weight: bold;
    display: block;
    margin-top: 5px;
}

.test-error {
    color: var(--accuflow-error-color);
    font-weight: bold;
    display: block;
    margin-top: 5px;
}

.spinner {
    vertical-align: middle;
    margin-right: 8px; /* More space */
    color: var(--accuflow-accent-color); /* Use accent color for spinner */
}

/* Column Settings Layout */
.accuflow-column-settings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)); /* Adjusted width */
    gap: 20px; /* More gap */
    margin-top: 15px;
    margin-bottom: 15px;
}

.accuflow-column-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}
.accuflow-column-item label {
    font-weight: 600;
    margin-bottom: 8px; /* More space */
    color: var(--accuflow-primary-color);
}
.accuflow-column-item input[type="number"] {
    width: 90px; /* Slightly wider */
    text-align: center;
    padding: 8px;
}

/* Product Links Table */
.accuflow-tab-content .wp-list-table {
    margin-top: 25px;
    border-radius: var(--accuflow-border-radius);
    overflow: hidden; /* For rounded corners on table */
}

.accuflow-tab-content .wp-list-table th,
.accuflow-tab-content .wp-list-table td {
    vertical-align: middle;
    padding: 15px 15px; /* More padding */
    border-bottom: 1px solid var(--accuflow-border-color);
}
.accuflow-tab-content .wp-list-table th {
    background-color: var(--accuflow-light-bg);
    color: var(--accuflow-primary-color);
    font-weight: 600;
    text-transform: uppercase; /* Uppercase table headers */
    font-size: 0.9em;
}


.accuflow-fulfillment-method {
    min-width: 160px; /* Slightly wider */
    padding: 8px;
}

.accuflow-fulfillment-config {
    margin-top: 15px;
    border-left: 4px solid var(--accuflow-info-color); /* Thicker border */
    padding-left: 15px;
    background-color: var(--accuflow-light-bg);
    padding: 15px;
    border-radius: var(--accuflow-border-radius);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05); /* Subtle inner shadow */
}
.accuflow-fulfillment-config label {
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
    color: var(--accuflow-primary-color);
}
.accuflow-fulfillment-config p.description {
    margin-top: 5px;
    margin-bottom: 5px;
}

.accuflow-fulfillment-config input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.1); /* Slightly larger checkboxes */
}
.accuflow-api-custom-link-regex-field {
    margin-top: 15px;
    border-top: 1px dashed var(--accuflow-border-color); /* Dashed border */
    padding-top: 15px;
}

.accuflow-sheet-config-buttons,
.accuflow-api-config-buttons {
    margin-top: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.accuflow-sheet-config-buttons .button,
.accuflow-api-config-buttons .button {
    margin-bottom: 0; /* Remove default margin */
    width: auto;
    min-width: 140px; /* Wider buttons */
    padding: 8px 15px;
    font-weight: 500;
    background-color: var(--accuflow-button-secondary-bg);
    color: var(--accuflow-button-secondary-text);
    border-color: var(--accuflow-button-secondary-bg);
    box-shadow: none; /* No extra shadow here */
}
.accuflow-sheet-config-buttons .button:hover,
.accuflow-api-config-buttons .button:hover {
    background-color: var(--accuflow-border-color);
    color: var(--accuflow-primary-color);
    transform: translateY(-1px);
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
}


/* Tab Navigation */
.accuflow-tabs {
    border-bottom: 1px solid var(--accuflow-border-color);
    margin-bottom: 0; /* Remove margin, tab-content handles it */
    display: flex;
    gap: 0; /* No gap for seamless look */
    background-color: var(--accuflow-light-bg);
    border-radius: var(--accuflow-border-radius) var(--accuflow-border-radius) 0 0;
    overflow: hidden; /* For smooth corners */
}

.accuflow-tab-button {
    background-color: var(--accuflow-light-bg);
    border: 1px solid var(--accuflow-border-color);
    border-bottom: none;
    padding: 12px 25px; /* More padding */
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease-in-out;
    color: var(--accuflow-secondary-color);
    text-decoration: none;
    white-space: nowrap;
    position: relative;
    z-index: 1; /* For stacking order */
    border-radius: var(--accuflow-border-radius) var(--accuflow-border-radius) 0 0; /* Round top corners */
    margin-right: -1px; /* Overlap borders */
}

.accuflow-tab-button:hover {
    background-color: #f0f3f6; /* Slightly darker */
    color: var(--accuflow-primary-color);
}

.accuflow-tab-button.active {
    background-color: var(--accuflow-card-bg);
    border-color: var(--accuflow-border-color);
    color: var(--accuflow-primary-color);
    box-shadow: -2px -2px 8px rgba(0, 0, 0, 0.03); /* Subtle shadow for active tab */
    z-index: 2; /* Bring active tab to front */
    top: 1px; /* Create illusion of being above border */
    border-bottom-color: transparent; /* Seamless with content */
}

.accuflow-tab-content {
    background-color: var(--accuflow-card-bg);
    border: 1px solid var(--accuflow-border-color);
    border-top: none;
    padding: 30px; /* More padding */
    border-radius: 0 0 var(--accuflow-border-radius) var(--accuflow-border-radius);
    box-shadow: var(--accuflow-box-shadow);
    margin-bottom: 30px; /* Consistent margin for the whole section */
    padding-top: 20px; /* Adjust padding for tab content */
}


/* Toggle Details (for JSON converter & troubleshooting) */
.accuflow-toggle-details {
    margin-bottom: 25px; /* More spacing */
    background-color: var(--accuflow-light-bg);
    border: 1px solid var(--accuflow-border-color);
    border-radius: var(--accuflow-border-radius);
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.03); /* Subtle inner shadow */
}

.accuflow-toggle-summary {
    background-color: #eaf3f8; /* Softer blue for summary */
    padding: 18px 25px; /* More padding */
    font-size: 1.25em; /* Slightly larger */
    font-weight: 600;
    cursor: pointer;
    color: var(--accuflow-primary-color);
    border-bottom: 1px solid var(--accuflow-border-color);
    display: flex;
    align-items: center;
    transition: all 0.2s ease-in-out;
}
.accuflow-toggle-summary:hover {
    background-color: #e0ecf4; /* Darker on hover */
}


.accuflow-toggle-summary .dashicons {
    margin-right: 12px;
    color: var(--accuflow-info-color);
    transition: transform 0.2s ease-in-out;
}

.accuflow-toggle-details[open] .accuflow-toggle-summary .dashicons {
    transform: rotate(90deg);
}

.accuflow-toggle-content {
    padding: 25px; /* More padding */
    background-color: var(--accuflow-card-bg); /* Inner content background */
}

.accuflow-actions-row {
    margin-top: 20px;
    display: flex;
    gap: 15px;
}

/* Pagination links within the table nav */
.tablenav-pages .pagination-links .button {
    background-color: var(--accuflow-light-bg);
    border-color: var(--accuflow-border-color);
    color: var(--accuflow-primary-color);
    border-radius: var(--accuflow-border-radius);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease-in-out;
    padding: 8px 12px; /* Adjusted padding */
}
.tablenav-pages .pagination-links .button.current {
    background-color: var(--accuflow-accent-color);
    border-color: var(--accuflow-accent-color);
    color: #fff;
}
.tablenav-pages .pagination-links .button:hover {
    background-color: #e0e6ea;
    color: var(--accuflow-hover-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
}
.tablenav-pages .pagination-links .button.current:hover {
    background-color: #a3854c; /* Darker accent */
    border-color: #a3854c;
}
.tablenav-pages .displaying-num {
    color: var(--accuflow-secondary-color);
    margin-right: 15px;
}


/* WC Email Management Specific Styles */
.accuflow-wc-email-toggle summary {
    display: flex;
    align-items: center;
    gap: 15px; /* More space */
    cursor: pointer; /* Ensure pointer cursor */
}

.accuflow-wc-email-toggle summary strong {
    flex-grow: 1;
    font-size: 1.1em; /* Slightly larger text */
}

.accuflow-wc-email-toggle summary .dashicons {
    transition: transform 0.2s ease-in-out;
    font-size: 1.1em;
    color: var(--accuflow-primary-color);
}

.accuflow-wc-email-toggle[open] summary .dashicons {
    transform: rotate(90deg);
}

.accuflow-wc-email-toggle .accuflow-toggle-content {
    background-color: #fdfdfd; /* Lighter background for inner content */
    padding-top: 15px;
}

.accuflow-wc-email-toggle .warning-text {
    color: var(--accuflow-warning-color);
    font-weight: bold;
    margin-top: 10px;
    margin-bottom: 15px;
    background-color: #fff8e6; /* Light warning background */
    border: 1px solid #ffebcc;
    padding: 10px 15px;
    border-radius: var(--accuflow-border-radius);
    display: inline-block; /* To contain background */
}
/* Ensure proper alignment for checkbox and text in summary */
.accuflow-wc-email-enable-checkbox {
    transform: scale(1.2); /* Make checkbox slightly larger */
    margin-right: 5px;
}

/* Responsive Adjustments */
@media screen and (max-width: 782px) {
    .accuflow-card .form-table th,
    .accuflow-card .form-table td {
        display: block;
        width: 100%;
        padding-left: 15px; /* Adjust padding for mobile */
        padding-right: 15px;
    }
    .accuflow-card .form-table th {
        padding-bottom: 5px;
        font-size: 1.1em;
    }
    .accuflow-card .form-table td {
        padding-top: 5px;
        padding-bottom: 15px;
    }
    .accuflow-column-settings {
        grid-template-columns: 1fr;
    }
    .accuflow-submit-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    .accuflow-submit-buttons .button {
        width: 100%;
        margin-right: 0;
        margin-bottom: 15px;
    }
    .accuflow-tabs {
        flex-wrap: wrap;
        border-radius: 0;
    }
    .accuflow-tab-button {
        flex-grow: 1;
        text-align: center;
        border-radius: 0;
        margin-right: 0;
        border-bottom: 1px solid var(--accuflow-border-color);
    }
    .accuflow-tab-button.active {
        border-bottom: none;
        top: 0;
        box-shadow: none;
    }
    .accuflow-tab-content {
        border-radius: 0;
        padding: 20px;
    }
    .accuflow-toggle-summary {
        padding: 15px;
        font-size: 1.1em;
    }
    .accuflow-toggle-content {
        padding: 15px;
    }
    .tablenav-pages .pagination-links {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 5px;
    }
    .tablenav-pages .pagination-links .button {
        flex-grow: 1;
        min-width: unset;
    }
    .tablenav.top .alignleft.actions {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 15px;
    }
    .tablenav.top .alignleft.actions input[type="search"] {
        flex-grow: 1;
        max-width: none;
    }
}
/* admin/css/admin-styles.css (Thêm vào cuối file) */

/* Email Preview Modal */
.accuflow-email-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99999; /* High z-index to be on top */
}

.accuflow-preview-content {
    position: relative;
    background-color: #fff;
    padding: 20px;
    border-radius: var(--accuflow-border-radius);
    box-shadow: var(--accuflow-box-shadow);
    max-width: 800px; /* Default max-width for PC preview */
    width: 90%; /* Responsive width */
    height: 90%; /* Responsive height */
    display: flex;
    flex-direction: column;
}

.accuflow-close-preview-modal {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 2em;
    color: var(--accuflow-secondary-color);
    transition: color 0.2s ease-in-out;
    z-index: 10; /* Above iframe */
    padding: 0;
    line-height: 1;
}

.accuflow-close-preview-modal .dashicons {
    font-size: 1em;
    width: 1em;
    height: 1em;
}

.accuflow-close-preview-modal:hover {
    color: var(--accuflow-error-color);
}

.accuflow-preview-iframe {
    flex-grow: 1; /* Make iframe fill available space */
    border: 1px solid var(--accuflow-border-color);
    border-radius: var(--accuflow-border-radius);
    background-color: #fff;
    width: 100%;
    height: 100%; /* Will be overridden by JS for mobile preview */
}

/* Style for sender settings card */
.accuflow-sender-settings {
    margin-bottom: 30px;
}
.accuflow-sender-settings .form-table th {
    width: 20%; /* Adjust width for sender settings labels */
}