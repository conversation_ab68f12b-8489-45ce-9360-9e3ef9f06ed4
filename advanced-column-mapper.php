<?php
/**
 * Advanced Column Mapper - Gi<PERSON>i pháp linh hoạt cho column mapping
 */

// Load WordPress
$wp_load_path = '../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('Cannot find WordPress. Please check path.');
}

// Security check
if (!current_user_can('manage_options')) {
    die('Permission denied. Please login as admin.');
}

echo "<h1>🎯 Advanced Column Mapper</h1>";
echo "<p><strong>Configuration time:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Handle form submissions
$action_performed = false;
$action_message = '';

if ($_POST) {
    $solution_type = $_POST['solution_type'] ?? '';
    
    try {
        $current_options = get_option('accuflow_settings', []);
        
        if ($solution_type === 'custom' && isset($_POST['custom_columns'])) {
            // Custom mapping solution
            $custom_columns = $_POST['custom_columns'];
            
            // Validate input
            $valid_columns = [];
            foreach ($custom_columns as $field => $index) {
                $index = intval($index);
                if ($index >= 0 && $index <= 25) {
                    $valid_columns[$field] = $index;
                }
            }
            
            if (count($valid_columns) === 14) {
                $current_options['columns'] = $valid_columns;
                
                // Update mapping strings
                $field_names = array_keys($valid_columns);
                $indices = array_values($valid_columns);
                $current_options['column_mapping_string'] = implode('|', $field_names);
                $current_options['column_indices_string'] = implode('|', $indices);
                
                update_option('accuflow_settings', $current_options);
                $action_performed = true;
                $action_message = "✅ Custom column mapping applied successfully!";
            } else {
                $action_message = "❌ Invalid column indices provided.";
            }
            
        } elseif ($solution_type === 'reset' && isset($_POST['confirm_reset'])) {
            // Reset to default A-N order
            $default_columns = [
                'ID' => 0, 'Username' => 1, 'Password' => 2, 'Login_URL' => 3,
                'Status' => 4, 'Order_ID' => 5, 'Sold_Date' => 6, 'Expiration_Date' => 7,
                'Platform' => 8, 'Plan' => 9, 'Price' => 10, 'Payment_Status' => 11,
                'Notes' => 12, 'Variation_Attribute' => 13
            ];
            
            $current_options['columns'] = $default_columns;
            $current_options['column_mapping_string'] = 'ID|Username|Password|Login_URL|Status|Order_ID|Sold_Date|Expiration_Date|Platform|Plan|Price|Payment_Status|Notes|Variation_Attribute';
            $current_options['column_indices_string'] = '0|1|2|3|4|5|6|7|8|9|10|11|12|13';
            
            update_option('accuflow_settings', $current_options);
            $action_performed = true;
            $action_message = "✅ Column mapping reset to A-N order successfully!";
            
        } elseif ($solution_type === 'auto_detect') {
            // Auto-detect from Google Sheet
            $action_message = "🔍 Auto-detection feature coming soon...";
        }
        
    } catch (Exception $e) {
        $action_message = "❌ Error: " . $e->getMessage();
    }
}

// Show action result
if ($action_performed) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>{$action_message}</h3>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>Run <strong>check-current-config.php</strong> to verify changes</li>";
    echo "<li>Run <strong>debug-sheet-data.php</strong> to test Google Sheet reading</li>";
    echo "<li>Test account fulfillment</li>";
    echo "</ul>";
    echo "</div>";
} elseif (!empty($action_message)) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>{$action_message}</h3>";
    echo "</div>";
}

// Get current configuration
try {
    $config = new AccuFlow_Config();
    $settings = $config->get_config();
    
    echo "<h2>📊 Current Configuration Analysis</h2>";
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    // Show current mapping
    echo "<h4>Current Column Mapping:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #e9ecef; font-weight: bold;'>";
    echo "<th>Field</th><th>Index</th><th>Excel Column</th><th>Issues</th></tr>";
    
    $issues_found = [];
    $used_indices = [];
    
    foreach ($settings['columns'] as $field => $index) {
        $excel_col = chr(65 + $index);
        $issues = [];
        
        // Check for duplicate indices
        if (in_array($index, $used_indices)) {
            $issues[] = "Duplicate index";
            $issues_found[] = "Field '{$field}' has duplicate index {$index}";
        }
        $used_indices[] = $index;
        
        // Check for out of range
        if ($index < 0 || $index > 25) {
            $issues[] = "Out of range";
            $issues_found[] = "Field '{$field}' has invalid index {$index}";
        }
        
        $issue_text = empty($issues) ? '✅ OK' : '❌ ' . implode(', ', $issues);
        $row_color = empty($issues) ? 'background: #d4edda;' : 'background: #f8d7da;';
        
        echo "<tr style='{$row_color}'>";
        echo "<td><strong>{$field}</strong></td>";
        echo "<td>{$index}</td>";
        echo "<td>{$excel_col}</td>";
        echo "<td>{$issue_text}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if (!empty($issues_found)) {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0;'>";
        echo "<h4>⚠️ Issues Found:</h4>";
        echo "<ul>";
        foreach ($issues_found as $issue) {
            echo "<li>{$issue}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    echo "</div>";
    
    // Show solutions
    echo "<h2>🛠️ Choose Your Solution</h2>";
    
    // Solution 1: Custom Mapping
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>💡 Solution 1: Custom Column Mapping</h3>";
    echo "<p><strong>Best for:</strong> When you want to keep your current Google Sheet structure</p>";
    echo "<form method='post'>";
    echo "<input type='hidden' name='solution_type' value='custom'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px; margin: 10px 0;'>";
    echo "<tr style='background: #e9ecef; font-weight: bold;'>";
    echo "<th style='width: 200px;'>Field Name</th><th style='width: 120px;'>Current Index</th><th style='width: 120px;'>Current Column</th><th>Set New Index (0=A, 1=B, 2=C...)</th></tr>";
    
    foreach ($settings['columns'] as $field => $current_index) {
        $current_col = chr(65 + $current_index);
        echo "<tr>";
        echo "<td><strong>{$field}</strong></td>";
        echo "<td>{$current_index}</td>";
        echo "<td>{$current_col}</td>";
        echo "<td><input type='number' name='custom_columns[{$field}]' value='{$current_index}' min='0' max='25' style='width: 80px; padding: 5px;'></td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p><button type='submit' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>Apply Custom Mapping</button></p>";
    echo "</form>";
    echo "</div>";
    
    // Solution 2: Reset to A-N
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🔄 Solution 2: Reset to Standard A-N Order</h3>";
    echo "<p><strong>Best for:</strong> When you're OK with rearranging your Google Sheet</p>";
    echo "<p><strong>This will set:</strong></p>";
    echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin: 10px 0;'>";
    echo "<div>";
    echo "<ul style='margin: 0;'>";
    echo "<li><strong>A:</strong> ID</li>";
    echo "<li><strong>B:</strong> Username</li>";
    echo "<li><strong>C:</strong> Password</li>";
    echo "<li><strong>D:</strong> Login_URL</li>";
    echo "<li><strong>E:</strong> Status</li>";
    echo "<li><strong>F:</strong> Order_ID</li>";
    echo "<li><strong>G:</strong> Sold_Date</li>";
    echo "</ul>";
    echo "</div>";
    echo "<div>";
    echo "<ul style='margin: 0;'>";
    echo "<li><strong>H:</strong> Expiration_Date</li>";
    echo "<li><strong>I:</strong> Platform</li>";
    echo "<li><strong>J:</strong> Plan</li>";
    echo "<li><strong>K:</strong> Price</li>";
    echo "<li><strong>L:</strong> Payment_Status</li>";
    echo "<li><strong>M:</strong> Notes</li>";
    echo "<li><strong>N:</strong> Variation_Attribute</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
    echo "<form method='post'>";
    echo "<input type='hidden' name='solution_type' value='reset'>";
    echo "<p><input type='checkbox' id='confirm_reset' name='confirm_reset' value='yes'> ";
    echo "<label for='confirm_reset'>I understand I need to rearrange my Google Sheet to match A-N order</label></p>";
    echo "<p><button type='submit' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>Reset to A-N Order</button></p>";
    echo "</form>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<p>❌ <strong>Error loading configuration:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>📋 Recommendations</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>Which solution should you choose?</h4>";
echo "<ul>";
echo "<li><strong>Choose Custom Mapping if:</strong> You have existing data and don't want to rearrange your sheet</li>";
echo "<li><strong>Choose A-N Reset if:</strong> You want the simplest, most reliable setup</li>";
echo "<li><strong>Most common issue:</strong> Multiple fields pointing to the same column (like all pointing to column A)</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><em>Advanced mapper completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
}

h1 { 
    color: #2271b1; 
    border-bottom: 3px solid #2271b1; 
    padding-bottom: 10px; 
}

h2 { 
    color: #135e96; 
    border-bottom: 2px solid #ddd; 
    padding-bottom: 5px; 
    margin-top: 30px; 
}

h3, h4 { 
    color: #0073aa; 
    margin-top: 0;
}

table {
    width: 100%;
    margin: 10px 0;
    font-size: 12px;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background: #f0f0f0;
    font-weight: bold;
}

input[type="number"] {
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    font-size: 14px;
    cursor: pointer;
}

button:hover {
    opacity: 0.9;
}
</style>
