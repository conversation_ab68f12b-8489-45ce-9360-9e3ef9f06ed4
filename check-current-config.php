<?php
/**
 * <PERSON><PERSON>t kiểm tra cấu hình hiện tại của AccuFlow
 */

// Load WordPress
$wp_load_path = '../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('Cannot find WordPress. Please check path.');
}

// Security check
if (!current_user_can('manage_options')) {
    die('Permission denied. Please login as admin.');
}

echo "<h1>🔧 Current AccuFlow Configuration</h1>";
echo "<p><strong>Check time:</strong> " . date('Y-m-d H:i:s') . "</p>";

try {
    $config = new AccuFlow_Config();
    $settings = $config->get_config();
    
    echo "<h2>📋 Column Mapping Configuration</h2>";
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #e9ecef; font-weight: bold;'>";
    echo "<th>Field Name</th><th>Column Index</th><th>Excel Column</th><th>Expected Position</th>";
    echo "</tr>";
    
    foreach ($settings['columns'] as $field_name => $col_index) {
        $excel_col = chr(65 + $col_index);
        $expected_position = '';
        
        switch($field_name) {
            case 'ID': $expected_position = 'A (0)'; break;
            case 'Username': $expected_position = 'B (1)'; break;
            case 'Password': $expected_position = 'C (2)'; break;
            case 'Login_URL': $expected_position = 'D (3)'; break;
            case 'Status': $expected_position = 'E (4)'; break;
            case 'Order_ID': $expected_position = 'F (5)'; break;
            case 'Sold_Date': $expected_position = 'G (6)'; break;
            case 'Expiration_Date': $expected_position = 'H (7)'; break;
            case 'Platform': $expected_position = 'I (8)'; break;
            case 'Plan': $expected_position = 'J (9)'; break;
            case 'Price': $expected_position = 'K (10)'; break;
            case 'Payment_Status': $expected_position = 'L (11)'; break;
            case 'Notes': $expected_position = 'M (12)'; break;
            case 'Variation_Attribute': $expected_position = 'N (13)'; break;
        }
        
        $status_color = ($excel_col . ' (' . $col_index . ')' === $expected_position) ? 'background: #d4edda;' : 'background: #f8d7da;';
        
        echo "<tr style='{$status_color}'>";
        echo "<td><strong>{$field_name}</strong></td>";
        echo "<td>{$col_index}</td>";
        echo "<td>{$excel_col}</td>";
        echo "<td>{$expected_position}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    echo "<h2>🔍 Status Configuration</h2>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    echo "<li><strong>Status Available:</strong> '{$settings['status_available']}'</li>";
    echo "<li><strong>Status Sold:</strong> '{$settings['status_sold']}'</li>";
    echo "<li><strong>Status Column Index:</strong> {$settings['columns']['Status']} (Column " . chr(65 + $settings['columns']['Status']) . ")</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 WordPress Options Check</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    // Check WordPress options
    $wp_options = get_option('accuflow_settings', []);
    
    echo "<h4>WordPress Database Settings:</h4>";
    if (empty($wp_options)) {
        echo "<p>❌ No AccuFlow settings found in WordPress options</p>";
    } else {
        echo "<pre>" . print_r($wp_options, true) . "</pre>";
    }
    
    // Check if column mapping was overridden
    if (isset($wp_options['columns'])) {
        echo "<h4>⚠️ Column mapping found in WordPress options (this overrides default config):</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #e9ecef; font-weight: bold;'>";
        echo "<th>Field</th><th>WP Option Index</th><th>Default Index</th><th>Status</th>";
        echo "</tr>";
        
        foreach ($settings['columns'] as $field => $default_index) {
            $wp_index = isset($wp_options['columns'][$field]) ? $wp_options['columns'][$field] : 'Not Set';
            $status = ($wp_index == $default_index) ? '✅ Match' : '❌ Different';
            
            echo "<tr>";
            echo "<td>{$field}</td>";
            echo "<td>{$wp_index}</td>";
            echo "<td>{$default_index}</td>";
            echo "<td>{$status}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    
    // Test actual Google Sheets reading
    echo "<h2>🧪 Live Google Sheets Test</h2>";
    
    if (!empty($settings['spreadsheet_id'])) {
        $utils = new AccuFlow_Utils();
        $google_api = new AccuFlow_Google_API($config, $utils);
        $service = $google_api->get_sheets_service();
        
        if ($service) {
            try {
                $spreadsheet = $service->spreadsheets->get($settings['spreadsheet_id']);
                $sheets = $spreadsheet->getSheets();
                
                if (!empty($sheets)) {
                    $first_sheet = $sheets[0];
                    $sheet_name = $first_sheet->getProperties()->getTitle();
                    
                    // Read first few rows
                    $range = $sheet_name . '!A1:N5';
                    $response = $service->spreadsheets_values->get($settings['spreadsheet_id'], $range);
                    $values = $response->getValues();
                    
                    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                    echo "<h4>First 5 rows from sheet '{$sheet_name}':</h4>";
                    
                    if (!empty($values)) {
                        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
                        
                        // Header with column letters
                        echo "<tr style='background: #e9ecef; font-weight: bold;'>";
                        for ($i = 0; $i < 14; $i++) {
                            echo "<th>" . chr(65 + $i) . "</th>";
                        }
                        echo "</tr>";
                        
                        foreach ($values as $row_index => $row) {
                            echo "<tr>";
                            for ($i = 0; $i < 14; $i++) {
                                $cell_value = isset($row[$i]) ? htmlspecialchars($row[$i]) : '';
                                
                                // Highlight status column
                                $bg_color = '';
                                if ($i == $settings['columns']['Status']) {
                                    $bg_color = 'background: #d4edda; font-weight: bold;';
                                }
                                
                                echo "<td style='padding: 3px; {$bg_color}'>{$cell_value}</td>";
                            }
                            echo "</tr>";
                        }
                        echo "</table>";
                        
                        // Show what's being read as status
                        echo "<h4>Status Column Analysis:</h4>";
                        echo "<ul>";
                        $status_col_index = $settings['columns']['Status'];
                        echo "<li><strong>Status column configured as:</strong> Index {$status_col_index} (Column " . chr(65 + $status_col_index) . ")</li>";
                        
                        foreach ($values as $row_index => $row) {
                            if ($row_index == 0) continue; // Skip header
                            $status_value = isset($row[$status_col_index]) ? $row[$status_col_index] : '[EMPTY]';
                            echo "<li><strong>Row " . ($row_index + 1) . " Status:</strong> '{$status_value}'</li>";
                            if ($row_index >= 3) break; // Show first 3 data rows
                        }
                        echo "</ul>";
                        
                    } else {
                        echo "<p>❌ No data found in sheet</p>";
                    }
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
                echo "<p>❌ Error reading Google Sheet: " . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "<p>❌ Cannot connect to Google Sheets service</p>";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<p>⚠️ Spreadsheet ID not configured</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Configuration check completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
}

h1 { 
    color: #2271b1; 
    border-bottom: 3px solid #2271b1; 
    padding-bottom: 10px; 
}

h2 { 
    color: #135e96; 
    border-bottom: 2px solid #ddd; 
    padding-bottom: 5px; 
    margin-top: 30px; 
}

h3, h4 { 
    color: #0073aa; 
    margin-top: 0;
}

table {
    width: 100%;
    margin: 10px 0;
    font-size: 12px;
}

th, td {
    padding: 5px 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background: #f0f0f0;
    font-weight: bold;
}

pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 11px;
    max-height: 300px;
    overflow-y: auto;
}
</style>
