<?php
/**
 * Clean Column Mapper - <PERSON><PERSON><PERSON> diện sạch sẽ và log chuẩn
 */

// Load WordPress
$wp_load_path = '../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('Cannot find WordPress. Please check path.');
}

// Security check
if (!current_user_can('manage_options')) {
    die('Permission denied. Please login as admin.');
}

echo "<h1>🎯 Clean Column Mapper</h1>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Handle form submission
$action_result = '';

if ($_POST) {
    try {
        if (isset($_POST['force_clean_reset'])) {
            // Force clean reset
            delete_option('accuflow_settings');
            
            $clean_mapping = [
                'ID' => 0, 'Username' => 1, 'Password' => 2, 'Login_URL' => 3,
                'Status' => 4, 'Order_ID' => 5, 'Sold_Date' => 6, 'Expiration_Date' => 7,
                'Platform' => 8, 'Plan' => 9, 'Price' => 10, 'Payment_Status' => 11,
                'Notes' => 12, 'Variation_Attribute' => 13
            ];
            
            $clean_options = [
                'columns' => $clean_mapping,
                'column_mapping_string' => 'ID|Username|Password|Login_URL|Status|Order_ID|Sold_Date|Expiration_Date|Platform|Plan|Price|Payment_Status|Notes|Variation_Attribute',
                'column_indices_string' => '0|1|2|3|4|5|6|7|8|9|10|11|12|13',
                'status_available' => 'Available',
                'status_sold' => 'Sold',
                'spreadsheet_id' => get_option('accuflow_settings')['spreadsheet_id'] ?? ''
            ];
            
            update_option('accuflow_settings', $clean_options);
            $action_result = "✅ Clean reset completed successfully!";
            
        } elseif (isset($_POST['update_mapping'])) {
            // Update column mapping
            $current_options = get_option('accuflow_settings', []);
            
            $new_mapping = [
                'ID' => intval($_POST['col_id'] ?? 0),
                'Username' => intval($_POST['col_username'] ?? 1),
                'Password' => intval($_POST['col_password'] ?? 2),
                'Login_URL' => intval($_POST['col_login_url'] ?? 3),
                'Status' => intval($_POST['col_status'] ?? 4),
                'Order_ID' => intval($_POST['col_order_id'] ?? 5),
                'Sold_Date' => intval($_POST['col_sold_date'] ?? 6),
                'Expiration_Date' => intval($_POST['col_expiration_date'] ?? 7),
                'Platform' => intval($_POST['col_platform'] ?? 8),
                'Plan' => intval($_POST['col_plan'] ?? 9),
                'Price' => intval($_POST['col_price'] ?? 10),
                'Payment_Status' => intval($_POST['col_payment_status'] ?? 11),
                'Notes' => intval($_POST['col_notes'] ?? 12),
                'Variation_Attribute' => intval($_POST['col_variation_attribute'] ?? 13)
            ];
            
            $current_options['columns'] = $new_mapping;
            $current_options['column_mapping_string'] = implode('|', array_keys($new_mapping));
            $current_options['column_indices_string'] = implode('|', array_values($new_mapping));
            
            update_option('accuflow_settings', $current_options);
            $action_result = "✅ Column mapping updated successfully!";
        }
        
    } catch (Exception $e) {
        $action_result = "❌ Error: " . $e->getMessage();
    }
}

// Show result
if (!empty($action_result)) {
    $bg_color = strpos($action_result, '✅') !== false ? '#d4edda' : '#f8d7da';
    echo "<div style='background: {$bg_color}; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>{$action_result}</h3>";
    echo "</div>";
}

// Get current config
try {
    $config = new AccuFlow_Config();
    $settings = $config->get_config();
    
    // Check for issues
    $indices = array_values($settings['columns']);
    $duplicates = array_diff_assoc($indices, array_unique($indices));
    
    if (!empty($duplicates)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ DUPLICATE COLUMNS DETECTED!</h3>";
        echo "<p>Multiple fields pointing to same column. This breaks the system!</p>";
        echo "<form method='post' style='margin-top: 10px;'>";
        echo "<button type='submit' name='force_clean_reset' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>🔄 Force Clean Reset</button>";
        echo "</form>";
        echo "</div>";
    }
    
    // Simple column mapper
    echo "<h2>📝 Column Mapping (Chỉ 14 trường chính)</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<form method='post'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 13px;'>";
    echo "<tr style='background: #e9ecef; font-weight: bold;'>";
    echo "<th style='padding: 8px; width: 150px;'>Field</th>";
    echo "<th style='padding: 8px; width: 80px;'>Current</th>";
    echo "<th style='padding: 8px; width: 80px;'>Column #</th>";
    echo "<th style='padding: 8px;'>Description</th>";
    echo "</tr>";
    
    $field_info = [
        'ID' => ['name' => 'col_id', 'desc' => 'Account ID (1, 2, 3...)'],
        'Username' => ['name' => 'col_username', 'desc' => 'Login username/email'],
        'Password' => ['name' => 'col_password', 'desc' => 'Login password'],
        'Login_URL' => ['name' => 'col_login_url', 'desc' => 'Login page URL'],
        'Status' => ['name' => 'col_status', 'desc' => 'Available/Sold status'],
        'Order_ID' => ['name' => 'col_order_id', 'desc' => 'WooCommerce order ID'],
        'Sold_Date' => ['name' => 'col_sold_date', 'desc' => 'Date when sold'],
        'Expiration_Date' => ['name' => 'col_expiration_date', 'desc' => 'Account expiry date'],
        'Platform' => ['name' => 'col_platform', 'desc' => 'Platform name'],
        'Plan' => ['name' => 'col_plan', 'desc' => 'Product/plan name'],
        'Price' => ['name' => 'col_price', 'desc' => 'Sale price'],
        'Payment_Status' => ['name' => 'col_payment_status', 'desc' => 'Payment status'],
        'Notes' => ['name' => 'col_notes', 'desc' => 'Additional notes'],
        'Variation_Attribute' => ['name' => 'col_variation_attribute', 'desc' => 'Product variation']
    ];
    
    foreach ($field_info as $field => $info) {
        $current_index = $settings['columns'][$field];
        $current_col = chr(65 + $current_index);
        
        $row_style = '';
        if ($field == 'Status') $row_style = 'background: #d4edda; font-weight: bold;';
        elseif (in_array($field, ['Username', 'Password', 'Login_URL'])) $row_style = 'background: #fff3cd;';
        
        echo "<tr style='{$row_style}'>";
        echo "<td style='padding: 8px;'><strong>{$field}</strong></td>";
        echo "<td style='padding: 8px; text-align: center;'>{$current_col} ({$current_index})</td>";
        echo "<td style='padding: 8px; text-align: center;'>";
        echo "<input type='number' name='{$info['name']}' value='{$current_index}' min='0' max='25' style='width: 50px; padding: 4px; text-align: center;'>";
        echo "</td>";
        echo "<td style='padding: 8px;'>{$info['desc']}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "<p style='margin-top: 15px; text-align: center;'>";
    echo "<button type='submit' name='update_mapping' style='background: #007cba; color: white; padding: 12px 25px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;'>💾 Update Column Mapping</button>";
    echo "</p>";
    echo "</form>";
    echo "</div>";
    
    // Google Sheets Log Preview
    echo "<h2>📊 Google Sheets Log Preview</h2>";
    
    if (!empty($settings['spreadsheet_id'])) {
        try {
            $utils = new AccuFlow_Utils();
            $google_api = new AccuFlow_Google_API($config, $utils);
            $service = $google_api->get_sheets_service();
            
            if ($service) {
                $spreadsheet = $service->spreadsheets->get($settings['spreadsheet_id']);
                $sheets = $spreadsheet->getSheets();
                
                if (!empty($sheets)) {
                    $first_sheet = $sheets[0];
                    $sheet_name = $first_sheet->getProperties()->getTitle();
                    
                    // Read data
                    $range = $sheet_name . '!A:N';
                    $response = $service->spreadsheets_values->get($settings['spreadsheet_id'], $range);
                    $values = $response->getValues();
                    
                    if (!empty($values)) {
                        $header_row = isset($values[0]) ? $values[0] : [];
                        $data_rows = array_slice($values, 1, 5); // First 5 rows only
                        
                        echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0; overflow-x: auto;'>";
                        echo "<h4>📋 Standard Log Format (Copy this structure to other sheets)</h4>";
                        echo "<p><strong>Sheet:</strong> {$sheet_name}</p>";
                        
                        // Create the exact format from the image
                        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
                        
                        // Header row - exactly like the image
                        echo "<tr style='background: #90EE90; font-weight: bold; text-align: center;'>";
                        $headers = ['ID', 'Username', 'Password', 'Login_URL', 'Status', 'Order_ID', 'Sold_Date', 'Expiration_Date', 'Platform', 'Plan', 'Price', 'Payment_Status', 'Notes', 'Variation_Attribute'];
                        foreach ($headers as $header) {
                            echo "<th style='padding: 5px; border: 1px solid #000; min-width: 80px;'>{$header}</th>";
                        }
                        echo "</tr>";
                        
                        // Data rows with sample data like the image
                        $sample_data = [
                            ['1', '<EMAIL>', 'password123', 'https://example.com/login', 'Available', '1001', '2024-01-15 10:30:00', '2025-01-31', 'WooCommerce', 'Premium Plan', '19.99', 'completed', 'Active account', 'Premium'],
                            ['2', '<EMAIL>', 'password456', 'https://example.com/login', 'Sold', '1002', '2024-01-16 14:20:00', '2025-02-28', 'WooCommerce', 'Basic Plan', '9.99', 'completed', 'Sold to customer', 'Basic'],
                            ['3', '<EMAIL>', 'password789', 'https://example.com/login', 'Available', '', '', '2025-03-31', 'WooCommerce', 'Premium Plan', '19.99', '', 'Ready for sale', 'Premium']
                        ];
                        
                        foreach ($sample_data as $row_index => $sample_row) {
                            echo "<tr>";
                            foreach ($sample_row as $col_index => $cell_value) {
                                $bg_color = '';
                                if ($col_index == 4) { // Status column
                                    if ($cell_value == 'Available') $bg_color = 'background: #d4edda;';
                                    elseif ($cell_value == 'Sold') $bg_color = 'background: #f8d7da;';
                                }
                                echo "<td style='padding: 4px; border: 1px solid #ccc; {$bg_color}'>" . htmlspecialchars($cell_value) . "</td>";
                            }
                            echo "</tr>";
                        }
                        
                        // Show actual data from sheet
                        echo "<tr style='background: #e9ecef;'><td colspan='14' style='text-align: center; font-weight: bold; padding: 8px;'>--- ACTUAL DATA FROM YOUR SHEET ---</td></tr>";
                        
                        foreach ($data_rows as $row_index => $row) {
                            echo "<tr>";
                            foreach ($headers as $field) {
                                $col_index = $settings['columns'][$field];
                                $cell_value = isset($row[$col_index]) ? htmlspecialchars($row[$col_index]) : '';
                                
                                $bg_color = '';
                                if ($field == 'Status') {
                                    if (trim($cell_value) === $settings['status_available']) $bg_color = 'background: #d4edda; font-weight: bold;';
                                    elseif (trim($cell_value) === $settings['status_sold']) $bg_color = 'background: #f8d7da; font-weight: bold;';
                                    else $bg_color = 'background: #fff3cd;';
                                }
                                
                                echo "<td style='padding: 4px; border: 1px solid #ccc; {$bg_color}'>{$cell_value}</td>";
                            }
                            echo "</tr>";
                        }
                        
                        echo "</table>";
                        echo "</div>";
                        
                        // Test available account
                        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4>🧪 Available Account Test</h4>";
                        
                        $sheet_gid = $first_sheet->getProperties()->getSheetId();
                        $test_account = $google_api->find_first_available_account_in_gid($sheet_gid);
                        
                        if ($test_account) {
                            echo "<p>✅ <strong>SUCCESS!</strong> Found available account:</p>";
                            echo "<ul>";
                            echo "<li><strong>Row:</strong> {$test_account['row_number']}</li>";
                            echo "<li><strong>ID:</strong> " . htmlspecialchars($test_account['id']) . "</li>";
                            echo "<li><strong>Username:</strong> " . htmlspecialchars($test_account['username']) . "</li>";
                            echo "<li><strong>Status:</strong> Available ✅</li>";
                            echo "</ul>";
                        } else {
                            echo "<p>❌ <strong>No available account found!</strong></p>";
                            echo "<p>Check: Status column reading from correct position, values match exactly</p>";
                        }
                        echo "</div>";
                    }
                }
            }
        } catch (Exception $e) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
            echo "<p>⚠️ Cannot connect to Google Sheets: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<p>⚠️ Spreadsheet ID not configured</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h3>📋 Quick Reference</h3>";
echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Column Numbers:</strong></p>";
echo "<div style='display: grid; grid-template-columns: repeat(7, 1fr); gap: 5px; font-family: monospace; font-size: 12px;'>";
for ($i = 0; $i < 14; $i++) {
    $col_letter = chr(65 + $i);
    echo "<div style='text-align: center; padding: 5px; background: white; border: 1px solid #ddd; border-radius: 3px;'>";
    echo "<strong>{$col_letter}</strong><br>{$i}";
    echo "</div>";
}
echo "</div>";
echo "</div>";

echo "<hr>";
echo "<p><em>Clean mapper completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
}

h1 { 
    color: #2271b1; 
    border-bottom: 3px solid #2271b1; 
    padding-bottom: 10px; 
}

h2 { 
    color: #135e96; 
    border-bottom: 2px solid #ddd; 
    padding-bottom: 5px; 
    margin-top: 30px; 
}

h3, h4 { 
    color: #0073aa; 
    margin-top: 0;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background: #f0f0f0;
    font-weight: bold;
}

button {
    cursor: pointer;
    transition: all 0.3s;
}

button:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

input[type="number"] {
    border: 1px solid #ddd;
    border-radius: 3px;
}
</style>
