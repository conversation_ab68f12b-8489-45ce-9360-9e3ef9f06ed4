<?php
/**
 * Script tạo file Excel mẫu với định dạng đúng cho AccuFlow
 */

// Load WordPress
$wp_load_path = '../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('Cannot find WordPress. Please check path.');
}

// Security check
if (!current_user_can('manage_options')) {
    die('Permission denied. Please login as admin.');
}

echo "<h1>📋 AccuFlow Excel Template Creator</h1>";
echo "<p><strong>Created:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Tạo dữ liệu mẫu
$sample_data = [
    // Header row
    ['ID', 'Username', 'Password', 'Login_URL', 'Status', 'Order_ID', 'Sold_Date', 'Expiration_Date', 'Platform', 'Plan', 'Price', 'Payment_Status', 'Notes', 'Variation_Attribute'],
    
    // Sample data rows
    ['1', '<EMAIL>', 'password123', 'https://example.com/login', 'Available', '', '', '', '', '', '', '', '', 'Basic'],
    ['2', '<EMAIL>', 'password456', 'https://example.com/login', 'Available', '', '', '', '', '', '', '', '', 'Premium'],
    ['3', '<EMAIL>', 'password789', 'https://example.com/login', 'Sold', '1001', '2025-01-15 10:30:00', '2025-12-31', 'WooCommerce', 'Premium Plan', '99.99', 'completed', 'Order #1001 - <EMAIL>', 'Premium'],
    ['4', '<EMAIL>', 'passwordabc', 'https://example.com/login', 'Available', '', '', '', '', '', '', '', '', 'Basic'],
    ['5', '<EMAIL>', 'passworddef', 'https://example.com/login', 'Available', '', '', '', '', '', '', '', '', 'Premium'],
    ['6', '<EMAIL>', 'passwordghi', 'https://example.com/login', 'Sold', '1002', '2025-01-16 14:20:00', '2025-12-31', 'WooCommerce', 'Basic Plan', '49.99', 'completed', 'Order #1002 - <EMAIL>', 'Basic'],
    ['7', '<EMAIL>', 'passwordjkl', 'https://example.com/login', 'Available', '', '', '', '', '', '', '', '', 'Premium'],
    ['8', '<EMAIL>', 'passwordmno', 'https://example.com/login', 'Available', '', '', '', '', '', '', '', '', 'Basic'],
    ['9', '<EMAIL>', 'passwordpqr', 'https://example.com/login', 'Available', '', '', '', '', '', '', '', '', 'Premium'],
    ['10', '<EMAIL>', 'passwordstu', 'https://example.com/login', 'Available', '', '', '', '', '', '', '', '', 'Basic'],
];

echo "<h2>📊 Sample Data Structure</h2>";
echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p>Đây là cấu trúc dữ liệu mẫu cho Google Sheet của bạn:</p>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; overflow-x: auto;'>";
echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";

foreach ($sample_data as $row_index => $row) {
    echo "<tr" . ($row_index === 0 ? " style='background: #e9ecef; font-weight: bold;'" : "") . ">";
    foreach ($row as $col_index => $cell) {
        $bg_color = '';
        if ($row_index > 0) { // Not header
            if ($col_index === 4) $bg_color = 'background: #d4edda;'; // Status
            elseif ($col_index === 1) $bg_color = 'background: #fff3cd;'; // Username
            elseif ($col_index === 2) $bg_color = 'background: #f8d7da;'; // Password
        }
        echo "<td style='padding: 5px; {$bg_color}'>" . htmlspecialchars($cell) . "</td>";
    }
    echo "</tr>";
}

echo "</table>";
echo "</div>";

echo "<h2>📝 Column Mapping Explanation</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<ul>";
echo "<li><strong>Column A (ID):</strong> Unique identifier for each account (1, 2, 3...)</li>";
echo "<li><strong>Column B (Username):</strong> Login username/email</li>";
echo "<li><strong>Column C (Password):</strong> Login password</li>";
echo "<li><strong>Column D (Login_URL):</strong> URL where customers can login</li>";
echo "<li><strong>Column E (Status):</strong> 'Available' or 'Sold'</li>";
echo "<li><strong>Column F (Order_ID):</strong> WooCommerce order ID (filled when sold)</li>";
echo "<li><strong>Column G (Sold_Date):</strong> Date when account was sold</li>";
echo "<li><strong>Column H (Expiration_Date):</strong> Account expiration date</li>";
echo "<li><strong>Column I (Platform):</strong> Platform name (WooCommerce)</li>";
echo "<li><strong>Column J (Plan):</strong> Product/plan name</li>";
echo "<li><strong>Column K (Price):</strong> Sale price</li>";
echo "<li><strong>Column L (Payment_Status):</strong> Payment status</li>";
echo "<li><strong>Column M (Notes):</strong> Additional notes</li>";
echo "<li><strong>Column N (Variation_Attribute):</strong> Product variation (Basic, Premium...)</li>";
echo "</ul>";
echo "</div>";

echo "<h2>💾 CSV Export</h2>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p>Copy dữ liệu CSV dưới đây và paste vào Google Sheet của bạn:</p>";
echo "<textarea style='width: 100%; height: 200px; font-family: monospace; font-size: 12px;'>";

foreach ($sample_data as $row) {
    echo implode(',', array_map(function($cell) {
        // Escape commas and quotes in CSV
        if (strpos($cell, ',') !== false || strpos($cell, '"') !== false) {
            return '"' . str_replace('"', '""', $cell) . '"';
        }
        return $cell;
    }, $row)) . "\n";
}

echo "</textarea>";
echo "</div>";

echo "<h2>🔧 Instructions</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>Để sử dụng template này:</h4>";
echo "<ol>";
echo "<li><strong>Copy CSV data</strong> từ textarea ở trên</li>";
echo "<li><strong>Mở Google Sheet</strong> của bạn</li>";
echo "<li><strong>Xóa tất cả dữ liệu cũ</strong> (nếu có)</li>";
echo "<li><strong>Paste dữ liệu</strong> vào cell A1</li>";
echo "<li><strong>Chỉnh sửa dữ liệu</strong> theo tài khoản thực tế của bạn</li>";
echo "<li><strong>Đảm bảo</strong> cột Status chỉ chứa 'Available' hoặc 'Sold'</li>";
echo "<li><strong>Test lại</strong> bằng cách chạy debug-sheet-data.php</li>";
echo "</ol>";
echo "</div>";

echo "<h2>⚠️ Important Notes</h2>";
echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<ul>";
echo "<li><strong>Không thay đổi thứ tự cột</strong> - Plugin đã được cấu hình theo thứ tự A-N</li>";
echo "<li><strong>Status phải chính xác:</strong> 'Available' (không phải 'available' hay 'AVAILABLE')</li>";
echo "<li><strong>Không để trống</strong> các cột Username, Password, Login_URL cho tài khoản Available</li>";
echo "<li><strong>ID phải unique</strong> và không trùng lặp</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><em>Template created at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
}

h1 { 
    color: #2271b1; 
    border-bottom: 3px solid #2271b1; 
    padding-bottom: 10px; 
}

h2 { 
    color: #135e96; 
    border-bottom: 2px solid #ddd; 
    padding-bottom: 5px; 
    margin-top: 30px; 
}

h3, h4 { 
    color: #0073aa; 
    margin-top: 0;
}

table {
    width: 100%;
    margin: 10px 0;
    font-size: 12px;
}

th, td {
    padding: 5px 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background: #f0f0f0;
    font-weight: bold;
}

textarea {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
}

pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
}
</style>
