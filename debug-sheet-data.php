<?php
/**
 * Debug script để kiểm tra dữ liệu thực trong Google Sheet
 */

// Load WordPress
$wp_load_path = '../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('Cannot find WordPress. Please check path.');
}

// Security check
if (!current_user_can('manage_options')) {
    die('Permission denied. Please login as admin.');
}

echo "<h1>🔍 Debug Sheet Data</h1>";
echo "<p><strong>Debug time:</strong> " . date('Y-m-d H:i:s') . "</p>";

try {
    $config = new AccuFlow_Config();
    $utils = new AccuFlow_Utils();
    $google_api = new AccuFlow_Google_API($config, $utils);
    $settings = $config->get_config();
    
    if (empty($settings['spreadsheet_id'])) {
        die('<div style="background: #f8d7da; padding: 15px; border-radius: 5px;">❌ Spreadsheet ID not configured</div>');
    }
    
    echo "<h2>1. 📋 Configuration</h2>";
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Spreadsheet ID:</strong> " . substr($settings['spreadsheet_id'], 0, 20) . "...</p>";
    echo "<p><strong>Column Mapping:</strong></p>";
    echo "<pre>" . print_r($settings['columns'], true) . "</pre>";
    echo "</div>";
    
    // Get service
    $service = $google_api->get_sheets_service();
    if (!$service) {
        die('<div style="background: #f8d7da; padding: 15px; border-radius: 5px;">❌ Cannot connect to Google Sheets service</div>');
    }
    
    echo "<h2>2. 📊 Sheet Data Sample</h2>";
    
    // Get first sheet to test
    try {
        $spreadsheet = $service->spreadsheets->get($settings['spreadsheet_id']);
        $sheets = $spreadsheet->getSheets();
        
        if (empty($sheets)) {
            die('<div style="background: #f8d7da; padding: 15px; border-radius: 5px;">❌ No sheets found in spreadsheet</div>');
        }
        
        $first_sheet = $sheets[0];
        $sheet_name = $first_sheet->getProperties()->getTitle();
        $sheet_gid = $first_sheet->getProperties()->getSheetId();
        
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Testing Sheet:</strong> {$sheet_name} (GID: {$sheet_gid})</p>";
        echo "</div>";
        
        // Read data from A1:N20 to see more structure
        $range = $sheet_name . '!A1:N20';
        $response = $service->spreadsheets_values->get($settings['spreadsheet_id'], $range);
        $values = $response->getValues();

        // Also test with raw values to see exact data
        echo "<h3>🔍 Raw Data Analysis:</h3>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Total rows retrieved:</strong> " . count($values) . "</p>";
        echo "<p><strong>First row data:</strong></p>";
        if (!empty($values[0])) {
            echo "<pre>" . print_r($values[0], true) . "</pre>";
        }
        if (!empty($values[1])) {
            echo "<p><strong>Second row data:</strong></p>";
            echo "<pre>" . print_r($values[1], true) . "</pre>";
        }
        echo "</div>";
        
        if (empty($values)) {
            echo '<div style="background: #fff3cd; padding: 15px; border-radius: 5px;">⚠️ No data found in sheet</div>';
        } else {
            echo "<h3>📋 Raw Sheet Data (First 10 rows):</h3>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; overflow-x: auto;'>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
            
            // Header row
            echo "<tr style='background: #e9ecef; font-weight: bold;'>";
            for ($i = 0; $i < 14; $i++) {
                $col_letter = chr(65 + $i);
                echo "<th style='padding: 5px; min-width: 80px;'>{$col_letter}</th>";
            }
            echo "</tr>";
            
            // Data rows
            foreach ($values as $row_index => $row) {
                echo "<tr>";
                for ($i = 0; $i < 14; $i++) {
                    $cell_value = isset($row[$i]) ? htmlspecialchars($row[$i]) : '';
                    $bg_color = '';
                    
                    // Highlight important columns
                    if ($i == $settings['columns']['Status']) $bg_color = 'background: #d4edda;'; // Status
                    elseif ($i == $settings['columns']['Username']) $bg_color = 'background: #fff3cd;'; // Username
                    elseif ($i == $settings['columns']['Password']) $bg_color = 'background: #f8d7da;'; // Password
                    
                    echo "<td style='padding: 5px; {$bg_color}'>{$cell_value}</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
            
            // Analyze first available row
            echo "<h3>🔍 Analysis of Available Accounts:</h3>";
            
            $header_row = isset($values[0]) ? $values[0] : [];
            $data_rows = array_slice($values, 1); // Skip header
            
            echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Header Row Analysis:</h4>";
            echo "<ul>";
            foreach ($settings['columns'] as $field_name => $col_index) {
                $header_value = isset($header_row[$col_index]) ? $header_row[$col_index] : '[Empty]';
                echo "<li><strong>{$field_name}</strong> (Column " . chr(65 + $col_index) . "): {$header_value}</li>";
            }
            echo "</ul>";
            echo "</div>";
            
            $available_accounts = [];
            foreach ($data_rows as $row_index => $row) {
                $status = isset($row[$settings['columns']['Status']]) ? $row[$settings['columns']['Status']] : '';
                if (strtolower(trim($status)) === strtolower($settings['status_available'])) {
                    $account = [
                        'row' => $row_index + 2, // +2 because we skipped header and array is 0-based
                        'id' => isset($row[$settings['columns']['ID']]) ? $row[$settings['columns']['ID']] : '',
                        'username' => isset($row[$settings['columns']['Username']]) ? $row[$settings['columns']['Username']] : '',
                        'password' => isset($row[$settings['columns']['Password']]) ? $row[$settings['columns']['Password']] : '',
                        'login_url' => isset($row[$settings['columns']['Login_URL']]) ? $row[$settings['columns']['Login_URL']] : '',
                        'status' => $status,
                    ];
                    $available_accounts[] = $account;
                }
            }
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Available Accounts Found: " . count($available_accounts) . "</h4>";
            
            if (empty($available_accounts)) {
                echo "<p>❌ No accounts with status '{$settings['status_available']}' found</p>";
                
                // Show all unique status values
                $all_statuses = [];
                foreach ($data_rows as $row) {
                    $status = isset($row[$settings['columns']['Status']]) ? trim($row[$settings['columns']['Status']]) : '';
                    if (!empty($status)) {
                        $all_statuses[] = $status;
                    }
                }
                $unique_statuses = array_unique($all_statuses);
                echo "<p><strong>Status values found in sheet:</strong> " . implode(', ', $unique_statuses) . "</p>";
                
            } else {
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
                echo "<tr style='background: #e9ecef;'><th>Row</th><th>ID</th><th>Username</th><th>Password</th><th>Login URL</th><th>Status</th></tr>";
                
                foreach (array_slice($available_accounts, 0, 5) as $account) { // Show first 5
                    echo "<tr>";
                    echo "<td>{$account['row']}</td>";
                    echo "<td>" . htmlspecialchars($account['id']) . "</td>";
                    echo "<td>" . htmlspecialchars($account['username']) . "</td>";
                    echo "<td>" . htmlspecialchars($account['password']) . "</td>";
                    echo "<td>" . htmlspecialchars($account['login_url']) . "</td>";
                    echo "<td>" . htmlspecialchars($account['status']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                if (count($available_accounts) > 5) {
                    echo "<p><em>... and " . (count($available_accounts) - 5) . " more</em></p>";
                }
            }
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<p>❌ <strong>Error reading sheet data:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "<h2>3. 🔧 Recommendations</h2>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Based on the analysis above:</h4>";
    echo "<ol>";
    echo "<li><strong>Check your sheet structure:</strong> Make sure columns B, C, D contain actual account data, not just 'Available'</li>";
    echo "<li><strong>Verify status column:</strong> Make sure status column (E) contains exactly '{$settings['status_available']}' for available accounts</li>";
    echo "<li><strong>Sample data format:</strong>";
    echo "<ul>";
    echo "<li>Column A (ID): 1, 2, 3...</li>";
    echo "<li>Column B (Username): user1, user2, user3...</li>";
    echo "<li>Column C (Password): pass1, pass2, pass3...</li>";
    echo "<li>Column D (Login_URL): https://example.com/login</li>";
    echo "<li>Column E (Status): Available, Sold</li>";
    echo "</ul>";
    echo "</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Debug completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
}

h1 { 
    color: #2271b1; 
    border-bottom: 3px solid #2271b1; 
    padding-bottom: 10px; 
}

h2 { 
    color: #135e96; 
    border-bottom: 2px solid #ddd; 
    padding-bottom: 5px; 
    margin-top: 30px; 
}

h3, h4 { 
    color: #0073aa; 
    margin-top: 0;
}

table {
    width: 100%;
    margin: 10px 0;
    font-size: 12px;
}

th, td {
    padding: 5px 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background: #f0f0f0;
    font-weight: bold;
}

pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
}
</style>
