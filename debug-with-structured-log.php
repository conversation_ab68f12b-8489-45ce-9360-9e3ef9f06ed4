<?php
/**
 * Debug với structured log format
 */

// Load WordPress
$wp_load_path = '../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('Cannot find WordPress. Please check path.');
}

// Security check
if (!current_user_can('manage_options')) {
    die('Permission denied. Please login as admin.');
}

echo "<h1>🔍 Debug with Structured Log</h1>";
echo "<p><strong>Debug time:</strong> " . date('Y-m-d H:i:s') . "</p>";

try {
    $config = new AccuFlow_Config();
    $utils = new AccuFlow_Utils();
    $google_api = new AccuFlow_Google_API($config, $utils);
    $settings = $config->get_config();
    
    if (empty($settings['spreadsheet_id'])) {
        die('<div style="background: #f8d7da; padding: 15px; border-radius: 5px;">❌ Spreadsheet ID not configured</div>');
    }
    
    // Get service
    $service = $google_api->get_sheets_service();
    if (!$service) {
        die('<div style="background: #f8d7da; padding: 15px; border-radius: 5px;">❌ Cannot connect to Google Sheets service</div>');
    }
    
    echo "<h2>📋 Current Column Mapping</h2>";
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #e9ecef; font-weight: bold;'>";
    echo "<th>Field</th><th>Column</th><th>Index</th></tr>";
    
    foreach ($settings['columns'] as $field => $index) {
        $col_letter = chr(65 + $index);
        echo "<tr>";
        echo "<td><strong>{$field}</strong></td>";
        echo "<td>{$col_letter}</td>";
        echo "<td>{$index}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    echo "<h2>📊 Google Sheet Data Analysis</h2>";
    
    // Get first sheet
    $spreadsheet = $service->spreadsheets->get($settings['spreadsheet_id']);
    $sheets = $spreadsheet->getSheets();
    
    if (empty($sheets)) {
        die('<div style="background: #f8d7da; padding: 15px; border-radius: 5px;">❌ No sheets found</div>');
    }
    
    $first_sheet = $sheets[0];
    $sheet_name = $first_sheet->getProperties()->getTitle();
    $sheet_gid = $first_sheet->getProperties()->getSheetId();
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Sheet:</strong> {$sheet_name} (GID: {$sheet_gid})</p>";
    echo "</div>";
    
    // Read data
    $range = $sheet_name . '!A:N';
    $response = $service->spreadsheets_values->get($settings['spreadsheet_id'], $range);
    $values = $response->getValues();
    
    if (empty($values)) {
        die('<div style="background: #fff3cd; padding: 15px; border-radius: 5px;">⚠️ No data found in sheet</div>');
    }
    
    $header_row = isset($values[0]) ? $values[0] : [];
    $data_rows = array_slice($values, 1);
    
    echo "<h3>📋 Structured Data Log</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; overflow-x: auto;'>";
    
    // Header structure
    echo "<h4>Header Structure:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px; margin-bottom: 20px;'>";
    echo "<tr style='background: #e9ecef; font-weight: bold;'>";
    echo "<th>ID</th><th>Username</th><th>Password</th><th>Login_URL</th><th>Status</th><th>Order_ID</th><th>Sold_Date</th>";
    echo "<th>Expiration_Date</th><th>Platform</th><th>Plan</th><th>Price</th><th>Payment_Status</th><th>Notes</th><th>Variation_Attribute</th>";
    echo "</tr>";
    
    // Header values
    echo "<tr style='background: #fff3cd;'>";
    $field_order = ['ID', 'Username', 'Password', 'Login_URL', 'Status', 'Order_ID', 'Sold_Date', 'Expiration_Date', 'Platform', 'Plan', 'Price', 'Payment_Status', 'Notes', 'Variation_Attribute'];
    foreach ($field_order as $field) {
        $col_index = $settings['columns'][$field];
        $header_value = isset($header_row[$col_index]) ? htmlspecialchars($header_row[$col_index]) : '[EMPTY]';
        echo "<td style='padding: 5px;'>{$header_value}</td>";
    }
    echo "</tr>";
    echo "</table>";
    
    // Data rows structure
    echo "<h4>Data Rows (First 10 rows):</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
    echo "<tr style='background: #e9ecef; font-weight: bold;'>";
    echo "<th>Row</th><th>ID</th><th>Username</th><th>Password</th><th>Login_URL</th><th>Status</th><th>Order_ID</th><th>Sold_Date</th>";
    echo "<th>Expiration_Date</th><th>Platform</th><th>Plan</th><th>Price</th><th>Payment_Status</th><th>Notes</th><th>Variation_Attribute</th>";
    echo "</tr>";
    
    $available_count = 0;
    $sold_count = 0;
    
    foreach (array_slice($data_rows, 0, 10) as $row_index => $row) {
        $actual_row = $row_index + 2; // +2 because array is 0-based and we skip header
        
        echo "<tr>";
        echo "<td style='padding: 3px; font-weight: bold;'>{$actual_row}</td>";
        
        foreach ($field_order as $field) {
            $col_index = $settings['columns'][$field];
            $cell_value = isset($row[$col_index]) ? htmlspecialchars($row[$col_index]) : '';
            
            // Highlight important columns
            $bg_color = '';
            if ($field == 'Status') {
                if (trim($cell_value) === $settings['status_available']) {
                    $bg_color = 'background: #d4edda; font-weight: bold;';
                    $available_count++;
                } elseif (trim($cell_value) === $settings['status_sold']) {
                    $bg_color = 'background: #f8d7da; font-weight: bold;';
                    $sold_count++;
                } else {
                    $bg_color = 'background: #fff3cd; font-weight: bold;';
                }
            } elseif ($field == 'Username' && !empty($cell_value)) {
                $bg_color = 'background: #e7f3ff;';
            } elseif ($field == 'Password' && !empty($cell_value)) {
                $bg_color = 'background: #f0f8ff;';
            }
            
            echo "<td style='padding: 3px; {$bg_color}'>{$cell_value}</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // Summary
    echo "<h3>📊 Summary Analysis</h3>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    echo "<li><strong>Total data rows:</strong> " . count($data_rows) . "</li>";
    echo "<li><strong>Available accounts found:</strong> {$available_count}</li>";
    echo "<li><strong>Sold accounts found:</strong> {$sold_count}</li>";
    echo "</ul>";
    
    // Status column analysis
    $status_col_index = $settings['columns']['Status'];
    $status_col_letter = chr(65 + $status_col_index);
    echo "<p><strong>Status Column Analysis:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Status is reading from:</strong> Column {$status_col_letter} (index {$status_col_index})</li>";
    echo "<li><strong>Expected Available value:</strong> '{$settings['status_available']}'</li>";
    echo "<li><strong>Expected Sold value:</strong> '{$settings['status_sold']}'</li>";
    echo "</ul>";
    
    // Show unique status values
    $all_status_values = [];
    foreach ($data_rows as $row) {
        $status_value = isset($row[$status_col_index]) ? trim($row[$status_col_index]) : '';
        if (!empty($status_value)) {
            $all_status_values[] = $status_value;
        }
    }
    $unique_statuses = array_unique($all_status_values);
    echo "<p><strong>Unique status values found:</strong> " . implode(', ', array_map('htmlspecialchars', $unique_statuses)) . "</p>";
    echo "</div>";
    
    // Test account finding
    echo "<h3>🧪 Test Account Finding</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    $test_account = $google_api->find_first_available_account_in_gid($sheet_gid);
    
    if ($test_account) {
        echo "<h4>✅ Found Available Account:</h4>";
        echo "<table border='1' style='border-collapse: collapse; font-size: 12px;'>";
        echo "<tr style='background: #e9ecef; font-weight: bold;'>";
        echo "<th>Field</th><th>Value</th></tr>";
        echo "<tr><td><strong>Row Number</strong></td><td>{$test_account['row_number']}</td></tr>";
        echo "<tr><td><strong>ID</strong></td><td>" . htmlspecialchars($test_account['id']) . "</td></tr>";
        echo "<tr><td><strong>Username</strong></td><td>" . htmlspecialchars($test_account['username']) . "</td></tr>";
        echo "<tr><td><strong>Password</strong></td><td>" . htmlspecialchars($test_account['password']) . "</td></tr>";
        echo "<tr><td><strong>Login URL</strong></td><td>" . htmlspecialchars($test_account['login_url']) . "</td></tr>";
        echo "</table>";
    } else {
        echo "<h4>❌ No Available Account Found</h4>";
        echo "<p>Possible reasons:</p>";
        echo "<ul>";
        echo "<li>No rows with Status = '{$settings['status_available']}'</li>";
        echo "<li>Status column is reading from wrong column</li>";
        echo "<li>Status values don't match exactly (case-sensitive)</li>";
        echo "</ul>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>🔧 Quick Actions</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>If you see issues:</strong></p>";
echo "<ol>";
echo "<li><strong>Wrong column mapping:</strong> Use <a href='simple-column-mapper.php' target='_blank'>Simple Column Mapper</a></li>";
echo "<li><strong>Status not matching:</strong> Check your Google Sheet Status column values</li>";
echo "<li><strong>No available accounts:</strong> Make sure you have rows with Status = 'Available'</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><em>Debug completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
}

h1 { 
    color: #2271b1; 
    border-bottom: 3px solid #2271b1; 
    padding-bottom: 10px; 
}

h2 { 
    color: #135e96; 
    border-bottom: 2px solid #ddd; 
    padding-bottom: 5px; 
    margin-top: 30px; 
}

h3, h4 { 
    color: #0073aa; 
    margin-top: 0;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background: #f0f0f0;
    font-weight: bold;
}

a {
    color: #007cba;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
