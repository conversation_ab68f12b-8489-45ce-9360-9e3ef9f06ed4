<?php
/**
 * <PERSON>ript để kiểm tra và sửa lỗi dữ liệu trong Google Sheet
 */

// Load WordPress
$wp_load_path = '../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('Cannot find WordPress. Please check path.');
}

// Security check
if (!current_user_can('manage_options')) {
    die('Permission denied. Please login as admin.');
}

echo "<h1>🔧 Fix Sheet Data Issues</h1>";
echo "<p><strong>Analysis time:</strong> " . date('Y-m-d H:i:s') . "</p>";

try {
    $config = new AccuFlow_Config();
    $utils = new AccuFlow_Utils();
    $google_api = new AccuFlow_Google_API($config, $utils);
    $settings = $config->get_config();
    
    if (empty($settings['spreadsheet_id'])) {
        die('<div style="background: #f8d7da; padding: 15px; border-radius: 5px;">❌ Spreadsheet ID not configured</div>');
    }
    
    // Get service
    $service = $google_api->get_sheets_service();
    if (!$service) {
        die('<div style="background: #f8d7da; padding: 15px; border-radius: 5px;">❌ Cannot connect to Google Sheets service</div>');
    }
    
    echo "<h2>1. 🔍 Current Sheet Analysis</h2>";
    
    // Get first sheet
    $spreadsheet = $service->spreadsheets->get($settings['spreadsheet_id']);
    $sheets = $spreadsheet->getSheets();
    
    if (empty($sheets)) {
        die('<div style="background: #f8d7da; padding: 15px; border-radius: 5px;">❌ No sheets found</div>');
    }
    
    $first_sheet = $sheets[0];
    $sheet_name = $first_sheet->getProperties()->getTitle();
    $sheet_gid = $first_sheet->getProperties()->getSheetId();
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Analyzing Sheet:</strong> {$sheet_name} (GID: {$sheet_gid})</p>";
    echo "</div>";
    
    // Read all data
    $range = $sheet_name . '!A:N';
    $response = $service->spreadsheets_values->get($settings['spreadsheet_id'], $range);
    $values = $response->getValues();
    
    if (empty($values)) {
        die('<div style="background: #fff3cd; padding: 15px; border-radius: 5px;">⚠️ No data found in sheet</div>');
    }
    
    $header_row = isset($values[0]) ? $values[0] : [];
    $data_rows = array_slice($values, 1);
    
    echo "<h3>📊 Data Analysis Results:</h3>";
    
    // Analyze issues
    $issues = [];
    $fixes_needed = [];
    
    // Check header row
    $expected_headers = ['ID', 'Username', 'Password', 'Login_URL', 'Status', 'Order_ID', 'Sold_Date', 'Expiration_Date', 'Platform', 'Plan', 'Price', 'Payment_Status', 'Notes', 'Variation_Attribute'];
    
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Header Row Check:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr><th>Column</th><th>Expected</th><th>Current</th><th>Status</th></tr>";
    
    for ($i = 0; $i < 14; $i++) {
        $expected = $expected_headers[$i];
        $current = isset($header_row[$i]) ? $header_row[$i] : '[EMPTY]';
        $status = ($expected === $current) ? '✅ OK' : '❌ MISMATCH';
        
        if ($expected !== $current) {
            $issues[] = "Header mismatch in column " . chr(65 + $i) . ": expected '{$expected}', got '{$current}'";
            $fixes_needed[] = [
                'type' => 'header',
                'column' => $i,
                'expected' => $expected,
                'current' => $current
            ];
        }
        
        echo "<tr>";
        echo "<td>" . chr(65 + $i) . "</td>";
        echo "<td>{$expected}</td>";
        echo "<td>{$current}</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // Check data rows
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Data Rows Analysis:</h4>";
    
    $available_count = 0;
    $sold_count = 0;
    $invalid_status_count = 0;
    $empty_username_count = 0;
    $empty_password_count = 0;
    
    $status_values = [];
    
    foreach ($data_rows as $row_index => $row) {
        $actual_row_number = $row_index + 2; // +2 because array is 0-based and we skip header
        
        // Check status
        $status = isset($row[$settings['columns']['Status']]) ? trim($row[$settings['columns']['Status']]) : '';
        if (!empty($status)) {
            $status_values[] = $status;
            
            if ($status === $settings['status_available']) {
                $available_count++;
                
                // Check if available accounts have required data
                $username = isset($row[$settings['columns']['Username']]) ? trim($row[$settings['columns']['Username']]) : '';
                $password = isset($row[$settings['columns']['Password']]) ? trim($row[$settings['columns']['Password']]) : '';
                
                if (empty($username)) {
                    $empty_username_count++;
                    $issues[] = "Row {$actual_row_number}: Available account missing username";
                }
                
                if (empty($password)) {
                    $empty_password_count++;
                    $issues[] = "Row {$actual_row_number}: Available account missing password";
                }
                
            } elseif ($status === $settings['status_sold']) {
                $sold_count++;
            } else {
                $invalid_status_count++;
                $issues[] = "Row {$actual_row_number}: Invalid status '{$status}' (should be '{$settings['status_available']}' or '{$settings['status_sold']}')";
            }
        }
    }
    
    $unique_statuses = array_unique($status_values);
    
    echo "<ul>";
    echo "<li><strong>Total data rows:</strong> " . count($data_rows) . "</li>";
    echo "<li><strong>Available accounts:</strong> {$available_count}</li>";
    echo "<li><strong>Sold accounts:</strong> {$sold_count}</li>";
    echo "<li><strong>Invalid status:</strong> {$invalid_status_count}</li>";
    echo "<li><strong>Available accounts missing username:</strong> {$empty_username_count}</li>";
    echo "<li><strong>Available accounts missing password:</strong> {$empty_password_count}</li>";
    echo "<li><strong>Unique status values found:</strong> " . implode(', ', $unique_statuses) . "</li>";
    echo "</ul>";
    echo "</div>";
    
    // Show issues summary
    if (!empty($issues)) {
        echo "<h3>❌ Issues Found:</h3>";
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<ol>";
        foreach ($issues as $issue) {
            echo "<li>{$issue}</li>";
        }
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>✅ No Issues Found!</h3>";
        echo "<p>Your sheet data looks good!</p>";
        echo "</div>";
    }
    
    // Show first few available accounts
    echo "<h3>📋 Sample Available Accounts:</h3>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    $sample_accounts = [];
    foreach ($data_rows as $row_index => $row) {
        $status = isset($row[$settings['columns']['Status']]) ? trim($row[$settings['columns']['Status']]) : '';
        if ($status === $settings['status_available']) {
            $sample_accounts[] = [
                'row' => $row_index + 2,
                'id' => isset($row[$settings['columns']['ID']]) ? $row[$settings['columns']['ID']] : '',
                'username' => isset($row[$settings['columns']['Username']]) ? $row[$settings['columns']['Username']] : '',
                'password' => isset($row[$settings['columns']['Password']]) ? $row[$settings['columns']['Password']] : '',
                'login_url' => isset($row[$settings['columns']['Login_URL']]) ? $row[$settings['columns']['Login_URL']] : '',
            ];
            
            if (count($sample_accounts) >= 5) break; // Show first 5
        }
    }
    
    if (!empty($sample_accounts)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #e9ecef;'><th>Row</th><th>ID</th><th>Username</th><th>Password</th><th>Login URL</th></tr>";
        
        foreach ($sample_accounts as $account) {
            echo "<tr>";
            echo "<td>{$account['row']}</td>";
            echo "<td>" . htmlspecialchars($account['id']) . "</td>";
            echo "<td>" . htmlspecialchars($account['username']) . "</td>";
            echo "<td>" . htmlspecialchars($account['password']) . "</td>";
            echo "<td>" . htmlspecialchars($account['login_url']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No available accounts found with proper data</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>💡 Recommendations</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<ol>";
echo "<li><strong>Use the template:</strong> Run create-sample-excel-template.php to get proper format</li>";
echo "<li><strong>Check status values:</strong> Make sure they are exactly 'Available' or 'Sold' (case-sensitive)</li>";
echo "<li><strong>Fill required fields:</strong> Username, Password, Login_URL must not be empty for Available accounts</li>";
echo "<li><strong>Test again:</strong> Run debug-sheet-data.php after fixing</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><em>Analysis completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
}

h1 { 
    color: #2271b1; 
    border-bottom: 3px solid #2271b1; 
    padding-bottom: 10px; 
}

h2 { 
    color: #135e96; 
    border-bottom: 2px solid #ddd; 
    padding-bottom: 5px; 
    margin-top: 30px; 
}

h3, h4 { 
    color: #0073aa; 
    margin-top: 0;
}

table {
    width: 100%;
    margin: 10px 0;
    font-size: 12px;
}

th, td {
    padding: 5px 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background: #f0f0f0;
    font-weight: bold;
}
</style>
