<?php
/**
 * AccuFlow - Google Sheets Integration: Quản lý template email
 *
 * @package AccuFlow
 * @subpackage Includes
 * @since 2.5.0
 */

// Ngăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

if ( ! class_exists( 'AccuFlow_Email_Template' ) ) {
    /**
     * Lớp AccuFlow_Email_Template để tạo và quản lý template email.
     */
    class AccuFlow_Email_Template {

        /**
         * @var AccuFlow_Config Đối tượng cấu hình plugin.
         */
        private $config;

        /**
         * Khởi tạo lớp email template.
         *
         * @param AccuFlow_Config $config Đối tượng cấu hình.
         */
        public function __construct( AccuFlow_Config $config ) {
            $this->config = $config;
        }

        /**
         * Template HTML cho email gửi khách hàng.
         *
         * @param array $data Dữ liệu để điền vào template.
         * @return string HTML của email.
         */
        public function get_email_template_html( $data ) {
            $config_data = $this->config->get_config();
            
            $username        = isset( $data['username'] ) ? $data['username'] : 'N/A';
            $password        = isset( $data['password'] ) ? $data['password'] : 'N/A';
            $login_url       = isset( $data['login_url'] ) ? $data['login_url'] : $config_data['support_url'];
            $special_message = isset( $data['special_message'] ) ? $data['special_message'] : '';
            $call_to_action  = isset( $data['call_to_action'] ) ? $data['call_to_action'] : 'Đăng nhập ngay';
            $button_text     = isset( $data['button_text'] ) ? $data['button_text'] : 'Đăng nhập ngay';
            $order_id        = isset( $data['order_id'] ) ? $data['order_id'] : 'N/A'; // Thêm order ID

            // Lấy template tùy chỉnh từ cấu hình
            $custom_template = $config_data['custom_email_template_html'];

            if ( ! empty( $custom_template ) ) {
                // Sử dụng template tùy chỉnh và thay thế placeholders
                $template_html = $custom_template;
                $template_html = str_replace( '{{shop_name}}', esc_html( $config_data['shop_name'] ), $template_html );
                $template_html = str_replace( '{{shop_logo_url}}', esc_url( $config_data['shop_logo_url'] ), $template_html );
                $template_html = str_replace( '{{username}}', esc_html( $username ), $template_html );
                $template_html = str_replace( '{{password}}', esc_html( $password ), $template_html );
                $template_html = str_replace( '{{login_url}}', esc_url( $login_url ), $template_html );
                $template_html = str_replace( '{{special_message}}', esc_html( $special_message ), $template_html );
                $template_html = str_replace( '{{call_to_action}}', esc_html( $call_to_action ), $template_html );
                $template_html = str_replace( '{{button_text}}', esc_html( $button_text ), $template_html );
                $template_html = str_replace( '{{support_url}}', esc_url( $config_data['support_url'] ), $template_html );
                $template_html = str_replace( '{{support_text}}', esc_html( $config_data['support_text'] ), $template_html );
                $template_html = str_replace( '{{order_id}}', esc_html( $order_id ), $template_html ); // Thêm placeholder order_id
                return $template_html;
            }

            // Fallback về template mặc định nếu không có template tùy chỉnh
            $template = '
            <!DOCTYPE html>
            <html lang="vi">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Thông tin Tài khoản của bạn - ' . esc_html( $config_data['shop_name'] ) . '</title>
                <style>
                    /* Basic styling for email clients without full CSS support */
                    body {
                        font-family: Arial, sans-serif;
                        background-color: #f0f2f5;
                        margin: 0;
                        padding: 0;
                    }
                    .container {
                        max-width: 600px;
                        margin: 20px auto;
                        background-color: #ffffff;
                        padding: 30px;
                        border-radius: 8px;
                        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 25px;
                    }
                    .header img {
                        max-width: 120px;
                        height: auto;
                        border-radius: 4px;
                    }
                    .header h1 {
                        color: #333333;
                        font-size: 28px;
                        margin-top: 10px;
                    }
                    .content h2 {
                        color: #555555;
                        font-size: 22px;
                        margin-bottom: 15px;
                    }
                    .content p {
                        color: #666666;
                        line-height: 1.6;
                        margin-bottom: 15px;
                    }
                    .account-info {
                        background-color: #e0f2f7; /* Light blue */
                        border-left: 5px solid #2196f3; /* Blue */
                        padding: 20px;
                        border-radius: 6px;
                        margin-bottom: 25px;
                        text-align: left;
                    }
                    .account-info strong {
                        color: #333333;
                    }
                    .account-info span {
                        font-family: monospace;
                        font-size: 1.1em;
                        color: #0d47a1; /* Darker blue */
                        word-break: break-all;
                    }
                    .button-container {
                        text-align: center;
                        margin-top: 30px;
                        margin-bottom: 30px;
                    }
                    .button {
                        display: inline-block;
                        padding: 12px 25px;
                        background-color: #4CAF50; /* Green */
                        color: #ffffff;
                        text-decoration: none;
                        border-radius: 5px;
                        font-size: 18px;
                        font-weight: bold;
                    }
                    .footer {
                        text-align: center;
                        margin-top: 25px;
                        color: #888888;
                        font-size: 13px;
                        line-height: 1.5;
                    }
                    .footer a {
                        color: #2196f3;
                        text-decoration: none;
                    }
                    .footer a:hover {
                        text-decoration: underline;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <img src="' . esc_url( $config_data['shop_logo_url'] ) . '" alt="' . esc_attr( $config_data['shop_name'] ) . '">
                        <h1>' . esc_html( $config_data['shop_name'] ) . '</h1>
                    </div>

                    <div class="content">
                        <h2>Chào mừng bạn đến với hệ thống của chúng tôi!</h2>
                        <p>
                            Thông tin tài khoản của bạn cho đơn hàng <strong>#{{order_id}}</strong> đã sẵn sàng.
                        </p>

                        <div class="account-info">
                            <p><strong>Tên đăng nhập (Username):</strong> <span style="font-mono text-lg text-blue-700">' . esc_html( $username ) . '</span></p>
                            <p><strong>Mật khẩu (Password):</strong> <span style="font-mono text-lg text-blue-700">' . esc_html( $password ) . '</span></p>
                            <p style="margin-top: 15px; font-size: 0.9em; color: #0d47a1;">
                                <em>' . esc_html( $special_message ) . '</em>
                            </p>
                        </div>

                        <p>
                            Bạn có thể ' . esc_html( $call_to_action ) . ' bằng cách nhấp vào nút dưới đây:
                        </p>

                        <div class="button-container">
                            <a href="' . esc_url( $login_url ) . '"
                               class="inline-block py-3 px-8 bg-blue-600 hover:bg-blue-700 text-white text-xl font-bold rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105">
                                ' . esc_html( $button_text ) . '
                            </a>
                        </div>
                    </div>

                    <div class="footer">
                        Nếu bạn có bất kỳ câu hỏi nào, vui lòng liên hệ với bộ phận hỗ trợ của chúng tôi tại
                        <a href="' . esc_url( $config_data['support_url'] ) . '" class="text-blue-600 hover:underline">' . esc_html( $config_data['support_text'] ) . '</a>.
                        <br>
                        Cảm ơn bạn đã tin tưởng và sử dụng dịch vụ của chúng tôi!
                        <br>
                        Trân trọng,<br>
                        Đội ngũ ' . esc_html( $config_data['shop_name'] ) . '
                    </div>
                </div>
            </body>
            </html>';

            return $template;
        }
    }
}