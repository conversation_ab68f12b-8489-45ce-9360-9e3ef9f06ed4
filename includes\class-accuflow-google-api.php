<?php
/**
 * AccuFlow - Google Sheets Integration: <PERSON><PERSON><PERSON> tương tác với Google Sheets API
 *
 * @package AccuFlow
 * @subpackage Includes
 * @since 2.5.0
 */

// Ngăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

if ( ! class_exists( 'AccuFlow_Google_API' ) ) {
    /**
     * Lớp AccuFlow_Google_API để xử lý tất cả các tương tác với Google Sheets API.
     */
    class AccuFlow_Google_API {

        private $config;
        private $utils;
        private $service;

        public function __construct( AccuFlow_Config $config, AccuFlow_Utils $utils ) {
            $this->config = $config;
            $this->utils = $utils;
        }

        private function build_credentials_from_fields() {
            $config_data = $this->config->get_config();
            
            if ( ! empty( $config_data['service_account_project_id'] ) && ! empty( $config_data['service_account_private_key'] ) && ! empty( $config_data['service_account_client_email'] ) ) {
                $credentials = [
                    'type'                        => $config_data['service_account_type'],
                    'project_id'                  => $config_data['service_account_project_id'],
                    'private_key_id'              => $config_data['service_account_private_key_id'],
                    'private_key'                 => $config_data['service_account_private_key'],
                    'client_email'                => $config_data['service_account_client_email'],
                    'client_id'                   => $config_data['service_account_client_id'],
                    'auth_uri'                    => 'https://accounts.google.com/o/oauth2/auth',
                    'token_uri'                   => 'https://oauth2.googleapis.com/token',
                    'auth_provider_x509_cert_url' => 'https://www.googleapis.com/oauth2/v1/certs',
                    'client_x509_cert_url'        => $config_data['service_account_client_x509_cert_url']
                ];
                return $credentials;
            }
            return false;
        }

        public function get_sheets_service() {
            if ( $this->service instanceof Google_Service_Sheets ) {
                return $this->service;
            }
            $client = new Google_Client();
            $client->setApplicationName( 'AccuFlow - WooCommerce Google Sheets Integration' );
            $client->setScopes( [ Google_Service_Sheets::SPREADSHEETS ] );
            try {
                $credentials = $this->build_credentials_from_fields();
                if ( ! $credentials ) {
                    return null;
                }
                $required_fields = ['type', 'project_id', 'private_key', 'client_email'];
                foreach ( $required_fields as $field ) {
                    if ( ! isset( $credentials[$field] ) || empty( $credentials[$field] ) ) {
                        return null;
                    }
                }
                $credentials = $this->utils->fix_private_key( $credentials );
                if ( ! $this->utils->test_private_key( $credentials['private_key'] ) ) {
                    return null;
                }
                $client->setAuthConfig( $credentials );
                $client_options = [ 'timeout' => 30, 'connect_timeout' => 10, ];
                $ca_cert_path = ACCUFLOW_PLUGIN_DIR . 'certs/cacert.pem';
                $disable_ssl_verify = $this->config->get_setting( 'disable_ssl_verify', false );
                if ( $disable_ssl_verify ) {
                    $client_options['verify'] = false;
                } elseif ( file_exists( $ca_cert_path ) ) {
                    $client_options['verify'] = $ca_cert_path;
                } else {
                    $client_options['verify'] = true;
                }
                $client->setHttpClient( new \GuzzleHttp\Client( $client_options ) );
                $this->service = new Google_Service_Sheets( $client );
                return $this->service;
            } catch ( Exception $e ) {
                error_log( 'AccuFlow Sheets Error: Lỗi xác thực Google Client: ' . $e->getMessage() );
                return null;
            }
        }

        public function get_sheet_name_from_gid( $spreadsheet_id, $gid ) {
            $service = $this->get_sheets_service();
            if ( ! $service ) return null;
            try {
                $spreadsheet = $service->spreadsheets->get( $spreadsheet_id );
                $sheets = $spreadsheet->getSheets();
                foreach ( $sheets as $sheet ) {
                    if ( $sheet->getProperties()->getSheetId() == $gid ) {
                        return $sheet->getProperties()->getTitle();
                    }
                }
                return null;
            } catch ( Exception $e ) {
                error_log( 'AccuFlow Sheets API Error (get_sheet_name_from_gid): ' . $e->getMessage() );
                return null;
            }
        }

        public function find_first_available_account_in_gid( $sheet_gid, $variation_attribute_value = null ) {
            $config_data = $this->config->get_config();
            $spreadsheetId = $config_data['spreadsheet_id'];
            $sheet_name = $this->get_sheet_name_from_gid( $spreadsheetId, $sheet_gid );
            if ( ! $sheet_name ) {
                return null;
            }
            $service = $this->get_sheets_service();
            if ( ! $service ) return null;

            $idColIndex = $config_data['columns']['ID'];
            $statusColIndex = $config_data['columns']['Status'];
            $variationAttrColIndex = $config_data['columns']['Variation_Attribute'];
            $enableVariationMatching = $config_data['enable_variation_matching'];

            $range = $sheet_name . '!A:N'; // Đọc đến cột N để bao gồm tất cả 14 cột

            try {
                $response = $service->spreadsheets_values->get( $spreadsheetId, $range );
                $values = $response->getValues();

                if ( empty( $values ) ) {
                    return null;
                }

                array_shift( $values );
                $row_index = 1;

                foreach ( $values as $row ) {
                    $row_index++;

                    // Debug: Log row data for troubleshooting
                    $status_value = isset( $row[$statusColIndex] ) ? $row[$statusColIndex] : '[EMPTY]';
                    error_log("AccuFlow Debug - Row {$row_index}: Status = '{$status_value}', Expected = '{$config_data['status_available']}'");

                    // Trim and compare status (case-sensitive)
                    $is_available = isset( $row[$statusColIndex] ) && trim($row[$statusColIndex]) === trim($config_data['status_available']);
                    if ( ! $is_available ) {
                        continue;
                    }

                    if ( $enableVariationMatching && $variation_attribute_value !== null ) {
                        $sheet_variation_value = isset( $row[$variationAttrColIndex] ) ? trim( $row[$variationAttrColIndex] ) : '';
                        if ( strcasecmp( trim( $variation_attribute_value ), $sheet_variation_value ) !== 0 ) {
                            continue;
                        }
                    }

                    $account_data = [
                        'id'         => isset( $row[$idColIndex] ) ? $row[$idColIndex] : '',
                        'username'   => isset( $row[$config_data['columns']['Username']] ) ? $row[$config_data['columns']['Username']] : '',
                        'password'   => isset( $row[$config_data['columns']['Password']] ) ? $row[$config_data['columns']['Password']] : '',
                        'login_url'  => isset( $row[$config_data['columns']['Login_URL']] ) ? $row[$config_data['columns']['Login_URL']] : '',
                        'row_number' => $row_index,
                    ];
                    return $account_data;
                }

                return null;

            } catch ( Exception $e ) {
                error_log( 'AccuFlow Sheets API Error (find_first_available_account_in_gid): ' . $e->getMessage() );
                return null;
            }
        }

        public function update_account_status( $sheet_gid, $row_number, $order_id, $expiration_date = null, $order = null ) {
            $config_data = $this->config->get_config();
            $spreadsheetId = $config_data['spreadsheet_id'];
            $sheet_name = $this->get_sheet_name_from_gid( $spreadsheetId, $sheet_gid );
            if ( ! $sheet_name ) return false;
            $service = $this->get_sheets_service();
            if ( ! $service ) return false;

            $data = [];

            // Cập nhật Status
            if (isset($config_data['columns']['Status'])) {
                $statusColChar = chr( 65 + $config_data['columns']['Status'] );
                $data[] = new Google_Service_Sheets_ValueRange([
                    'range' => $sheet_name . '!' . $statusColChar . $row_number,
                    'values' => [[$config_data['status_sold']]]
                ]);
            }

            // Cập nhật Order_ID
            if (isset($config_data['columns']['Order_ID'])) {
                $orderIdColChar = chr( 65 + $config_data['columns']['Order_ID'] );
                $data[] = new Google_Service_Sheets_ValueRange([
                    'range' => $sheet_name . '!' . $orderIdColChar . $row_number,
                    'values' => [[$order_id]]
                ]);
            }

            // Cập nhật Sold_Date
            if (isset($config_data['columns']['Sold_Date'])) {
                $soldDateColChar = chr( 65 + $config_data['columns']['Sold_Date'] );
                $data[] = new Google_Service_Sheets_ValueRange([
                    'range' => $sheet_name . '!' . $soldDateColChar . $row_number,
                    'values' => [[current_time('Y-m-d H:i:s')]]
                ]);
            }

            // Cập nhật Expiration_Date
            if ( $expiration_date !== null && isset($config_data['columns']['Expiration_Date']) ) {
                $expDateColChar = chr( 65 + $config_data['columns']['Expiration_Date'] );
                $data[] = new Google_Service_Sheets_ValueRange([
                    'range' => $sheet_name . '!' . $expDateColChar . $row_number,
                    'values' => [[$expiration_date]]
                ]);
            }

            // Cập nhật các cột mới nếu có order data
            if ($order) {
                // Platform
                if (isset($config_data['columns']['Platform'])) {
                    $platformColChar = chr( 65 + $config_data['columns']['Platform'] );
                    $data[] = new Google_Service_Sheets_ValueRange([
                        'range' => $sheet_name . '!' . $platformColChar . $row_number,
                        'values' => [['WooCommerce']]
                    ]);
                }

                // Plan (Product name)
                if (isset($config_data['columns']['Plan'])) {
                    $planColChar = chr( 65 + $config_data['columns']['Plan'] );
                    $product_name = '';
                    foreach ($order->get_items() as $item) {
                        $product_name = $item->get_name();
                        break; // Lấy sản phẩm đầu tiên
                    }
                    $data[] = new Google_Service_Sheets_ValueRange([
                        'range' => $sheet_name . '!' . $planColChar . $row_number,
                        'values' => [[$product_name]]
                    ]);
                }

                // Price
                if (isset($config_data['columns']['Price'])) {
                    $priceColChar = chr( 65 + $config_data['columns']['Price'] );
                    $data[] = new Google_Service_Sheets_ValueRange([
                        'range' => $sheet_name . '!' . $priceColChar . $row_number,
                        'values' => [[$order->get_total()]]
                    ]);
                }

                // Payment_Status
                if (isset($config_data['columns']['Payment_Status'])) {
                    $paymentStatusColChar = chr( 65 + $config_data['columns']['Payment_Status'] );
                    $data[] = new Google_Service_Sheets_ValueRange([
                        'range' => $sheet_name . '!' . $paymentStatusColChar . $row_number,
                        'values' => [[$order->get_status()]]
                    ]);
                }

                // Notes
                if (isset($config_data['columns']['Notes'])) {
                    $notesColChar = chr( 65 + $config_data['columns']['Notes'] );
                    $notes = sprintf('Order #%d - %s', $order_id, $order->get_billing_email());
                    $data[] = new Google_Service_Sheets_ValueRange([
                        'range' => $sheet_name . '!' . $notesColChar . $row_number,
                        'values' => [[$notes]]
                    ]);
                }
            }

            $batchUpdateRequest = new Google_Service_Sheets_BatchUpdateValuesRequest([
                'valueInputOption' => 'USER_ENTERED',
                'data' => $data
            ]);

            try {
                $service->spreadsheets_values->batchUpdate($spreadsheetId, $batchUpdateRequest);
                return true;
            } catch ( Exception $e ) {
                error_log( 'AccuFlow Sheets API Error (update_account_status batch): ' . $e->getMessage() );
                return false;
            }
        }
        
        public function write_test_log( $sheet_name, $sku, $test_type, $test_status, $message ) {
            $config_data = $this->config->get_config();
            $spreadsheetId = $config_data['spreadsheet_id'];
            $service = $this->get_sheets_service();
            if ( ! $service ) {
                return false;
            }

            $range = $sheet_name . '!A:F';
            $values = [
                [
                    current_time( 'mysql' ),
                    $test_type,
                    $test_status,
                    $sku,
                    $message,
                    $_SERVER['REMOTE_ADDR'] ?? 'N/A'
                ]
            ];

            $body = new Google_Service_Sheets_ValueRange( ['values' => $values] );
            $params = [
                'valueInputOption' => 'RAW'
            ];

            try {
                $service->spreadsheets_values->append( $spreadsheetId, $range, $body, $params );
                return true;
            } catch ( Exception $e ) {
                error_log( 'AccuFlow Sheets API Error (write_test_log): ' . $e->getMessage() );
                return false;
            }
        }
    }
}
