<?php
/**
 * AccuFlow - Google Sheets Integration: <PERSON><PERSON><PERSON> hàm tiện ích
 *
 * @package AccuFlow
 * @subpackage Includes
 * @since 2.5.0
 */

// Ngăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

if ( ! class_exists( 'AccuFlow_Utils' ) ) {
    /**
     * Lớp AccuFlow_Utils chứa các hàm tiện ích chung.
     */
    class AccuFlow_Utils {

        /**
         * @var AccuFlow_Config Đối tượng cấu hình plugin.
         */
        private $config;

        /**
         * Khởi tạo lớp tiện ích.
         *
         * @param AccuFlow_Config $config Đối tượng cấu hình.
         */
        public function __construct( AccuFlow_Config $config ) {
            $this->config = $config;
        }

        /**
         * Hàm sửa private key để đảm bảo format đúng PEM.
         *
         * @param array $credentials Mảng credentials.
         * @return array Mảng credentials với private_key đã được sửa.
         */
        public function fix_private_key( $credentials ) {
            if ( ! isset( $credentials['private_key'] ) || empty( $credentials['private_key'] ) ) {
                return $credentials;
            }

            $private_key = $credentials['private_key'];
            $original_key = $private_key;

            // Bước 1: Loại bỏ các khoảng trắng thừa ở đầu và cuối
            $private_key = trim( $private_key );

            // Bước 2: Chuẩn hóa các ký tự xuống dòng
            $private_key = str_replace( ["\r\n", "\r"], "\n", $private_key );

            // Bước 3: Đảm bảo có header và footer đúng và chỉ một lần
            $header = '-----BEGIN PRIVATE KEY-----';
            $footer = '-----END PRIVATE KEY-----';

            // Loại bỏ header/footer hiện có để thêm lại đúng cách
            $private_key = str_replace( $header, '', $private_key );
            $private_key = str_replace( $footer, '', $private_key );

            // Xóa các khoảng trắng và dòng trống thừa
            $lines = explode( "\n", $private_key );
            $cleaned_lines = [];
            foreach ( $lines as $line ) {
                $line = trim( $line );
                if ( ! empty( $line ) ) {
                    $cleaned_lines[] = $line;
                }
            }

            // Ghép lại phần body
            $body_content = implode( "\n", $cleaned_lines );

            // Thêm header và footer chuẩn PEM
            $private_key = $header . "\n" . $body_content . "\n" . $footer;

            // Kiểm tra cuối cùng độ dài (chỉ để log, không chặn)
            if ( strlen( $private_key ) < 100 ) {
                error_log( 'AccuFlow Sheets Warning: Private key quá ngắn sau khi xử lý (' . strlen( $private_key ) . ' ký tự). Original length: ' . strlen( $original_key ) );
            }

            $credentials['private_key'] = $private_key;
            error_log( 'AccuFlow Sheets Debug: Private key processed and cleaned (length: ' . strlen( $private_key ) . ')' );

            return $credentials;
        }

        /**
         * Hàm kiểm tra private key có thể được OpenSSL đọc được không.
         *
         * @param string $private_key Chuỗi private key đã được format.
         * @return bool True nếu OpenSSL có thể đọc, False nếu không.
         */
        public function test_private_key( $private_key ) {
            // Xóa các lỗi OpenSSL cũ
            while ( ( $error = openssl_error_string() ) !== false ) {
                // Clear previous errors
            }

            $resource = openssl_pkey_get_private( $private_key );

            if ( $resource === false ) {
                $error_string = '';
                while ( ( $error = openssl_error_string() ) !== false ) {
                    $error_string .= $error . "\n";
                }
                error_log( 'AccuFlow Sheets Error: OpenSSL không thể đọc private key: ' . $error_string );
                return false;
            }

            // Giải phóng resource
            if ( is_resource( $resource ) ) {
                openssl_free_key( $resource );
            }

            return true;
        }

        /**
         * Hàm kiểm tra môi trường PHP và OpenSSL.
         *
         * @return array Mảng các thông báo chẩn đoán.
         */
        public function check_php_environment() {
            $diagnosis = [];

            // Kiểm tra PHP version
            $php_version = phpversion();
            $diagnosis[] = "📋 **Thông tin môi trường PHP:**";
            $diagnosis[] = "➡️ PHP Version: " . $php_version;

            if ( version_compare( $php_version, '7.4', '<' ) ) {
                $diagnosis[] = "⚠️ PHP version cũ (" . $php_version . "). Khuyến nghị nâng cấp lên PHP 7.4+ hoặc 8.0+";
            } else {
                $diagnosis[] = "✅ PHP version tốt (" . $php_version . ")";
            }

            // Kiểm tra OpenSSL extension
            if ( extension_loaded( 'openssl' ) ) {
                $diagnosis[] = "✅ OpenSSL extension đã được tải";

                // Kiểm tra OpenSSL version
                if ( defined( 'OPENSSL_VERSION_TEXT' ) ) {
                    $diagnosis[] = "➡️ OpenSSL Version: " . OPENSSL_VERSION_TEXT;
                }

                // Kiểm tra các thuật toán OpenSSL
                $available_methods = openssl_get_md_methods();
                if ( in_array( 'sha256', $available_methods ) ) {
                    $diagnosis[] = "✅ SHA256 algorithm có sẵn";
                } else {
                    $diagnosis[] = "❌ SHA265 algorithm không có sẵn";
                }

                // Kiểm tra cipher methods
                $cipher_methods = openssl_get_cipher_methods();
                $diagnosis[] = "➡️ Số lượng cipher methods: " . count( $cipher_methods );

            } else {
                $diagnosis[] = "❌ OpenSSL extension KHÔNG được tải - Đây có thể là nguyên nhân chính!";
            }

            // Kiểm tra cURL extension
            if ( extension_loaded( 'curl' ) ) {
                $diagnosis[] = "✅ cURL extension đã được tải";

                $curl_version = curl_version();
                $diagnosis[] = "➡️ cURL Version: " . $curl_version['version'];
                $diagnosis[] = "➡️ SSL Version (cURL): " . $curl_version['ssl_version'];

                // Kiểm tra cURL có hỗ trợ SSL không
                if ( $curl_version['features'] & CURL_VERSION_SSL ) {
                    $diagnosis[] = "✅ cURL hỗ trợ SSL";
                } else {
                    $diagnosis[] = "❌ cURL KHÔNG hỗ trợ SSL";
                }

            } else {
                $diagnosis[] = "❌ cURL extension KHÔNG được tải";
            }

            // Kiểm tra JSON extension
            if ( extension_loaded( 'json' ) ) {
                $diagnosis[] = "✅ JSON extension đã được tải";
            } else {
                $diagnosis[] = "❌ JSON extension KHÔNG được tải";
            }

            // Kiểm tra memory limit
            $memory_limit = ini_get( 'memory_limit' );
            $diagnosis[] = "➡️ Memory Limit: " . $memory_limit;

            // Kiểm tra max execution time
            $max_execution_time = ini_get( 'max_execution_time' );
            $diagnosis[] = "➡️ Max Execution Time: " . $max_execution_time . " seconds";

            // Kiểm tra user agent
            $user_agent = ini_get( 'user_agent' );
            $diagnosis[] = "➡️ User Agent: " . ( $user_agent ?: 'Không đặt' );

            // Kiểm tra allow_url_fopen
            $allow_url_fopen = ini_get( 'allow_url_fopen' );
            $diagnosis[] = "➡️ allow_url_fopen: " . ( $allow_url_fopen ? 'Bật' : 'Tắt' );

            return $diagnosis;
        }

        /**
         * Hàm tự động tải xuống và cập nhật cacert.pem.
         *
         * @return bool True nếu tải xuống thành công, False nếu thất bại.
         */
        public function download_cacert() {
            $plugin_dir = ACCUFLOW_PLUGIN_DIR;
            $certs_dir = $plugin_dir . 'certs/';
            $cacert_path = $certs_dir . 'cacert.pem';

            // Tạo thư mục certs nếu chưa có
            if ( ! file_exists( $certs_dir ) ) {
                wp_mkdir_p( $certs_dir );
            }

            // URL để tải cacert.pem từ curl.se (official source)
            $cacert_url = 'https://curl.se/ca/cacert.pem';

            try {
                // Sử dụng WordPress HTTP API để tải file
                $response = wp_remote_get( $cacert_url, [
                    'timeout'   => 60,
                    'sslverify' => false, // Tạm thời tắt SSL verify để tải cacert
                ]);

                if ( is_wp_error( $response ) ) {
                    error_log( 'AccuFlow Sheets Error: Không thể tải cacert.pem: ' . $response->get_error_message() );
                    return false;
                }

                $http_code = wp_remote_retrieve_response_code( $response );
                if ( $http_code !== 200 ) {
                    error_log( 'AccuFlow Sheets Error: HTTP error khi tải cacert.pem: ' . $http_code );
                    return false;
                }

                $cacert_content = wp_remote_retrieve_body( $response );

                // Kiểm tra nội dung có hợp lệ không
                if ( empty( $cacert_content ) || strpos( $cacert_content, '-----BEGIN CERTIFICATE-----' ) === false ) {
                    error_log( 'AccuFlow Sheets Error: Nội dung cacert.pem không hợp lệ' );
                    return false;
                }

                // Lưu file
                $result = file_put_contents( $cacert_path, $cacert_content );

                if ( $result === false ) {
                    error_log( 'AccuFlow Sheets Error: Không thể ghi file cacert.pem' );
                    return false;
                }

                error_log( 'AccuFlow Sheets Success: Đã tải xuống cacert.pem thành công (' . strlen( $cacert_content ) . ' bytes)' );
                return true;

            } catch ( Exception $e ) {
                error_log( 'AccuFlow Sheets Error: Exception khi tải cacert.pem: ' . $e->getMessage() );
                return false;
            }
        }
    }
}