<?php
/**
 * Script reset column mapping về cấu hình mặc định
 */

// Load WordPress
$wp_load_path = '../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('Cannot find WordPress. Please check path.');
}

// Security check
if (!current_user_can('manage_options')) {
    die('Permission denied. Please login as admin.');
}

echo "<h1>🔄 Reset Column Mapping</h1>";
echo "<p><strong>Reset time:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Check if reset is requested
$reset_requested = isset($_POST['confirm_reset']) && $_POST['confirm_reset'] === 'yes';

if (!$reset_requested) {
    // Show current config and confirmation form
    try {
        $config = new AccuFlow_Config();
        $settings = $config->get_config();
        
        echo "<h2>📋 Current Column Mapping</h2>";
        echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #e9ecef; font-weight: bold;'>";
        echo "<th>Field Name</th><th>Current Index</th><th>Current Column</th><th>Default Index</th><th>Default Column</th>";
        echo "</tr>";
        
        $default_columns = [
            'ID'                  => 0,  // Cột A
            'Username'            => 1,  // Cột B
            'Password'            => 2,  // Cột C
            'Login_URL'           => 3,  // Cột D
            'Status'              => 4,  // Cột E
            'Order_ID'            => 5,  // Cột F
            'Sold_Date'           => 6,  // Cột G
            'Expiration_Date'     => 7,  // Cột H
            'Platform'            => 8,  // Cột I
            'Plan'                => 9,  // Cột J
            'Price'               => 10, // Cột K
            'Payment_Status'      => 11, // Cột L
            'Notes'               => 12, // Cột M
            'Variation_Attribute' => 13, // Cột N
        ];
        
        $has_differences = false;
        
        foreach ($default_columns as $field_name => $default_index) {
            $current_index = $settings['columns'][$field_name];
            $current_col = chr(65 + $current_index);
            $default_col = chr(65 + $default_index);
            
            $is_different = ($current_index != $default_index);
            if ($is_different) $has_differences = true;
            
            $row_color = $is_different ? 'background: #f8d7da;' : 'background: #d4edda;';
            
            echo "<tr style='{$row_color}'>";
            echo "<td><strong>{$field_name}</strong></td>";
            echo "<td>{$current_index}</td>";
            echo "<td>{$current_col}</td>";
            echo "<td>{$default_index}</td>";
            echo "<td>{$default_col}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        if ($has_differences) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>⚠️ Configuration Differences Detected</h3>";
            echo "<p>Your current column mapping is different from the default configuration.</p>";
            echo "<p><strong>This might be why the Status column is not working correctly!</strong></p>";
            echo "</div>";
            
            echo "<h2>🔄 Reset to Default</h2>";
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<form method='post'>";
            echo "<p><strong>⚠️ WARNING:</strong> This will reset your column mapping to default values:</p>";
            echo "<ul>";
            echo "<li>Status will be set to Column E (index 4)</li>";
            echo "<li>All other columns will be reset to A-N order</li>";
            echo "<li>This action cannot be undone</li>";
            echo "</ul>";
            echo "<p><input type='checkbox' id='confirm' name='confirm_reset' value='yes'> ";
            echo "<label for='confirm'>I understand and want to reset column mapping</label></p>";
            echo "<p><button type='submit' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>Reset Column Mapping</button></p>";
            echo "</form>";
            echo "</div>";
            
        } else {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>✅ Configuration is Correct</h3>";
            echo "<p>Your column mapping matches the default configuration.</p>";
            echo "<p>The issue might be in your Google Sheet data format.</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
} else {
    // Perform reset
    echo "<h2>🔄 Performing Reset...</h2>";
    
    try {
        // Get current WordPress options
        $current_options = get_option('accuflow_settings', []);
        
        // Reset column mapping to default
        $default_columns = [
            'ID'                  => 0,  // Cột A
            'Username'            => 1,  // Cột B
            'Password'            => 2,  // Cột C
            'Login_URL'           => 3,  // Cột D
            'Status'              => 4,  // Cột E
            'Order_ID'            => 5,  // Cột F
            'Sold_Date'           => 6,  // Cột G
            'Expiration_Date'     => 7,  // Cột H
            'Platform'            => 8,  // Cột I
            'Plan'                => 9,  // Cột J
            'Price'               => 10, // Cột K
            'Payment_Status'      => 11, // Cột L
            'Notes'               => 12, // Cột M
            'Variation_Attribute' => 13, // Cột N
        ];
        
        // Update WordPress options
        $current_options['columns'] = $default_columns;
        $current_options['column_mapping_string'] = 'ID|Username|Password|Login_URL|Status|Order_ID|Sold_Date|Expiration_Date|Platform|Plan|Price|Payment_Status|Notes|Variation_Attribute';
        $current_options['column_indices_string'] = '0|1|2|3|4|5|6|7|8|9|10|11|12|13';
        
        $updated = update_option('accuflow_settings', $current_options);
        
        if ($updated) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>✅ Reset Successful!</h3>";
            echo "<p>Column mapping has been reset to default values:</p>";
            echo "<ul>";
            foreach ($default_columns as $field => $index) {
                $col_letter = chr(65 + $index);
                echo "<li><strong>{$field}:</strong> Column {$col_letter} (index {$index})</li>";
            }
            echo "</ul>";
            echo "</div>";
            
            echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>🔍 Next Steps:</h4>";
            echo "<ol>";
            echo "<li>Run <strong>check-current-config.php</strong> to verify the reset</li>";
            echo "<li>Run <strong>debug-sheet-data.php</strong> to test Google Sheet reading</li>";
            echo "<li>Make sure your Google Sheet has Status values in Column E</li>";
            echo "<li>Test account fulfillment</li>";
            echo "</ol>";
            echo "</div>";
            
        } else {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>⚠️ Reset May Not Be Needed</h3>";
            echo "<p>WordPress reported no changes were made. This could mean:</p>";
            echo "<ul>";
            echo "<li>The configuration was already correct</li>";
            echo "<li>The values were the same as before</li>";
            echo "</ul>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<p>❌ <strong>Reset Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
}

echo "<hr>";
echo "<p><em>Reset operation completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
}

h1 { 
    color: #2271b1; 
    border-bottom: 3px solid #2271b1; 
    padding-bottom: 10px; 
}

h2 { 
    color: #135e96; 
    border-bottom: 2px solid #ddd; 
    padding-bottom: 5px; 
    margin-top: 30px; 
}

h3, h4 { 
    color: #0073aa; 
    margin-top: 0;
}

table {
    width: 100%;
    margin: 10px 0;
    font-size: 12px;
}

th, td {
    padding: 5px 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background: #f0f0f0;
    font-weight: bold;
}

form {
    margin: 0;
}

button {
    font-size: 14px;
}

button:hover {
    background: #c82333 !important;
}
</style>
