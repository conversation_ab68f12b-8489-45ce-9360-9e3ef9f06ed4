<?php
/**
 * Simple Column Mapper - Giải pháp đơn giản cho column mapping
 */

// Load WordPress
$wp_load_path = '../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('Cannot find WordPress. Please check path.');
}

// Security check
if (!current_user_can('manage_options')) {
    die('Permission denied. Please login as admin.');
}

echo "<h1>🎯 Simple Column Mapper</h1>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Handle form submission
if ($_POST && isset($_POST['update_mapping'])) {
    try {
        $current_options = get_option('accuflow_settings', []);
        
        // Simple mapping - just column letters
        $column_mapping = [
            'ID' => $_POST['col_id'] ?? 0,
            'Username' => $_POST['col_username'] ?? 1,
            'Password' => $_POST['col_password'] ?? 2,
            'Login_URL' => $_POST['col_login_url'] ?? 3,
            'Status' => $_POST['col_status'] ?? 4,
            'Order_ID' => $_POST['col_order_id'] ?? 5,
            'Sold_Date' => $_POST['col_sold_date'] ?? 6,
            'Expiration_Date' => $_POST['col_expiration_date'] ?? 7,
            'Platform' => $_POST['col_platform'] ?? 8,
            'Plan' => $_POST['col_plan'] ?? 9,
            'Price' => $_POST['col_price'] ?? 10,
            'Payment_Status' => $_POST['col_payment_status'] ?? 11,
            'Notes' => $_POST['col_notes'] ?? 12,
            'Variation_Attribute' => $_POST['col_variation_attribute'] ?? 13,
        ];
        
        // Convert to integers
        foreach ($column_mapping as $field => $value) {
            $column_mapping[$field] = intval($value);
        }
        
        $current_options['columns'] = $column_mapping;
        
        // Update mapping strings
        $field_names = array_keys($column_mapping);
        $indices = array_values($column_mapping);
        $current_options['column_mapping_string'] = implode('|', $field_names);
        $current_options['column_indices_string'] = implode('|', $indices);
        
        update_option('accuflow_settings', $current_options);
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>✅ Column mapping updated successfully!</h3>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ Error: " . htmlspecialchars($e->getMessage()) . "</h3>";
        echo "</div>";
    }
}

// Get current configuration
try {
    $config = new AccuFlow_Config();
    $settings = $config->get_config();
    
    echo "<h2>📊 Current Google Sheet Preview</h2>";
    
    // Try to get sheet data for preview
    if (!empty($settings['spreadsheet_id'])) {
        $utils = new AccuFlow_Utils();
        $google_api = new AccuFlow_Google_API($config, $utils);
        $service = $google_api->get_sheets_service();
        
        if ($service) {
            try {
                $spreadsheet = $service->spreadsheets->get($settings['spreadsheet_id']);
                $sheets = $spreadsheet->getSheets();
                
                if (!empty($sheets)) {
                    $first_sheet = $sheets[0];
                    $sheet_name = $first_sheet->getProperties()->getTitle();
                    
                    // Read first few rows
                    $range = $sheet_name . '!A1:N5';
                    $response = $service->spreadsheets_values->get($settings['spreadsheet_id'], $range);
                    $values = $response->getValues();
                    
                    if (!empty($values)) {
                        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; overflow-x: auto;'>";
                        echo "<h4>Sheet: {$sheet_name} (First 5 rows)</h4>";
                        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
                        
                        // Header with column letters and numbers
                        echo "<tr style='background: #e9ecef; font-weight: bold;'>";
                        for ($i = 0; $i < 14; $i++) {
                            $col_letter = chr(65 + $i);
                            echo "<th style='padding: 5px; min-width: 80px;'>{$col_letter}<br><small>({$i})</small></th>";
                        }
                        echo "</tr>";
                        
                        foreach ($values as $row_index => $row) {
                            echo "<tr>";
                            for ($i = 0; $i < 14; $i++) {
                                $cell_value = isset($row[$i]) ? htmlspecialchars($row[$i]) : '';
                                
                                // Highlight current mapping
                                $bg_color = '';
                                foreach ($settings['columns'] as $field => $col_index) {
                                    if ($col_index == $i) {
                                        if ($field == 'Status') $bg_color = 'background: #d4edda; font-weight: bold;';
                                        elseif ($field == 'Username') $bg_color = 'background: #fff3cd;';
                                        elseif ($field == 'Password') $bg_color = 'background: #f8d7da;';
                                        break;
                                    }
                                }
                                
                                echo "<td style='padding: 3px; {$bg_color}'>{$cell_value}</td>";
                            }
                            echo "</tr>";
                        }
                        echo "</table>";
                        echo "</div>";
                    }
                }
            } catch (Exception $e) {
                echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<p>⚠️ Cannot preview sheet: " . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
        }
    }
    
    echo "<h2>🎯 Simple Column Mapping</h2>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Hướng dẫn:</strong> Nhập số cột (0=A, 1=B, 2=C, 3=D, 4=E...) cho từng trường dữ liệu</p>";
    echo "</div>";
    
    echo "<form method='post'>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 14px;'>";
    echo "<tr style='background: #e9ecef; font-weight: bold;'>";
    echo "<th style='padding: 10px; width: 200px;'>Trường dữ liệu</th>";
    echo "<th style='padding: 10px; width: 100px;'>Hiện tại</th>";
    echo "<th style='padding: 10px; width: 150px;'>Cột mới (số)</th>";
    echo "<th style='padding: 10px;'>Mô tả</th>";
    echo "</tr>";
    
    $field_descriptions = [
        'ID' => 'Mã định danh tài khoản (1, 2, 3...)',
        'Username' => 'Tên đăng nhập / Email',
        'Password' => 'Mật khẩu',
        'Login_URL' => 'Link đăng nhập',
        'Status' => 'Trạng thái (Available/Sold)',
        'Order_ID' => 'Mã đơn hàng',
        'Sold_Date' => 'Ngày bán',
        'Expiration_Date' => 'Ngày hết hạn',
        'Platform' => 'Nền tảng (WooCommerce)',
        'Plan' => 'Tên gói/sản phẩm',
        'Price' => 'Giá bán',
        'Payment_Status' => 'Trạng thái thanh toán',
        'Notes' => 'Ghi chú',
        'Variation_Attribute' => 'Thuộc tính biến thể'
    ];
    
    $field_names = [
        'ID' => 'col_id',
        'Username' => 'col_username',
        'Password' => 'col_password',
        'Login_URL' => 'col_login_url',
        'Status' => 'col_status',
        'Order_ID' => 'col_order_id',
        'Sold_Date' => 'col_sold_date',
        'Expiration_Date' => 'col_expiration_date',
        'Platform' => 'col_platform',
        'Plan' => 'col_plan',
        'Price' => 'col_price',
        'Payment_Status' => 'col_payment_status',
        'Notes' => 'col_notes',
        'Variation_Attribute' => 'col_variation_attribute'
    ];
    
    foreach ($settings['columns'] as $field => $current_index) {
        $current_col = chr(65 + $current_index);
        $input_name = $field_names[$field];
        $description = $field_descriptions[$field];
        
        // Highlight important fields
        $row_style = '';
        if ($field == 'Status') $row_style = 'background: #d4edda;';
        elseif (in_array($field, ['Username', 'Password', 'Login_URL'])) $row_style = 'background: #fff3cd;';
        
        echo "<tr style='{$row_style}'>";
        echo "<td style='padding: 10px;'><strong>{$field}</strong></td>";
        echo "<td style='padding: 10px; text-align: center;'>{$current_col} ({$current_index})</td>";
        echo "<td style='padding: 10px; text-align: center;'>";
        echo "<input type='number' name='{$input_name}' value='{$current_index}' min='0' max='25' style='width: 60px; padding: 5px; text-align: center; font-size: 14px;'>";
        echo "</td>";
        echo "<td style='padding: 10px;'>{$description}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<button type='submit' name='update_mapping' style='background: #007cba; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;'>💾 Cập nhật Column Mapping</button>";
    echo "</div>";
    echo "</form>";
    
    // Quick reference
    echo "<h3>📋 Quick Reference</h3>";
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Số cột tương ứng:</strong></p>";
    echo "<div style='display: grid; grid-template-columns: repeat(7, 1fr); gap: 10px; font-family: monospace;'>";
    for ($i = 0; $i < 14; $i++) {
        $col_letter = chr(65 + $i);
        echo "<div style='text-align: center; padding: 5px; background: white; border: 1px solid #ddd; border-radius: 3px;'>";
        echo "<strong>{$col_letter}</strong><br><small>{$i}</small>";
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Simple mapper completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
}

h1 { 
    color: #2271b1; 
    border-bottom: 3px solid #2271b1; 
    padding-bottom: 10px; 
}

h2 { 
    color: #135e96; 
    border-bottom: 2px solid #ddd; 
    padding-bottom: 5px; 
    margin-top: 30px; 
}

h3, h4 { 
    color: #0073aa; 
    margin-top: 0;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background: #f0f0f0;
    font-weight: bold;
}

input[type="number"] {
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    cursor: pointer;
    transition: all 0.3s;
}

button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}
</style>
