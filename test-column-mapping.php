<?php
/**
 * Test column mapping với sheet structure mới (14 cột)
 */

// Load WordPress
$wp_load_path = '../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('Cannot find WordPress. Please check path.');
}

// Security check
if (!current_user_can('manage_options')) {
    die('Permission denied. Please login as admin.');
}

echo "<h1>🔧 Column Mapping Test - Version 2.5.0</h1>";
echo "<p><strong>Test time:</strong> " . date('Y-m-d H:i:s') . "</p>";

try {
    // Test 1: Load current configuration
    echo "<h2>1. 📋 Current Configuration</h2>";
    
    $config = new AccuFlow_Config();
    $settings = $config->get_config();
    
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>Column Mapping String:</h3>";
    echo "<code>" . esc_html($settings['column_mapping_string'] ?? 'Not set') . "</code><br><br>";
    
    echo "<h3>Column Indices String:</h3>";
    echo "<code>" . esc_html($settings['column_indices_string'] ?? 'Not set') . "</code><br><br>";
    
    echo "<h3>Static Columns Configuration:</h3>";
    echo "<pre>" . print_r($settings['columns'] ?? [], true) . "</pre>";
    echo "</div>";
    
    // Test 2: Expected vs Actual Sheet Structure
    echo "<h2>2. 📊 Sheet Structure Comparison</h2>";
    
    $expected_structure = [
        'A' => 'ID',
        'B' => 'Username', 
        'C' => 'Password',
        'D' => 'Login_URL',
        'E' => 'Status',
        'F' => 'Order_ID',
        'G' => 'Sold_Date',
        'H' => 'Expiration_Date',
        'I' => 'Platform',
        'J' => 'Plan',
        'K' => 'Price',
        'L' => 'Payment_Status',
        'M' => 'Notes',
        'N' => 'Variation_Attribute'
    ];
    
    $columns = $settings['columns'] ?? [];
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>📋 Expected vs Current Mapping:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Column</th><th>Expected Field</th><th>Current Mapping</th><th>Status</th></tr>";
    
    foreach ($expected_structure as $col_letter => $expected_field) {
        $col_index = ord($col_letter) - 65; // A=0, B=1, etc.
        $mapped_field = '';
        $status = '❌ Not mapped';
        
        // Find what field is mapped to this column
        foreach ($columns as $field_name => $field_index) {
            if ($field_index == $col_index) {
                $mapped_field = $field_name;
                if (strtolower($field_name) == strtolower($expected_field) || 
                    str_replace('_', '', strtolower($field_name)) == str_replace('_', '', strtolower($expected_field))) {
                    $status = '✅ Correct';
                } else {
                    $status = '⚠️ Different field';
                }
                break;
            }
        }
        
        $row_class = '';
        if (strpos($status, '✅') !== false) $row_class = 'style="background: #d4edda;"';
        elseif (strpos($status, '⚠️') !== false) $row_class = 'style="background: #fff3cd;"';
        elseif (strpos($status, '❌') !== false) $row_class = 'style="background: #f8d7da;"';
        
        echo "<tr {$row_class}>";
        echo "<td><strong>{$col_letter}</strong></td>";
        echo "<td>{$expected_field}</td>";
        echo "<td>{$mapped_field}</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // Test 3: Google API Range Test
    echo "<h2>3. 🔗 Google API Range Test</h2>";
    
    try {
        $utils = new AccuFlow_Utils();
        $google_api = new AccuFlow_Google_API($config, $utils);
        
        echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>Google API Configuration:</h3>";
        
        if (!empty($settings['spreadsheet_id'])) {
            echo "<p>✅ <strong>Spreadsheet ID:</strong> " . substr($settings['spreadsheet_id'], 0, 20) . "...</p>";
            echo "<p>📊 <strong>Reading Range:</strong> A:N (covers all 14 columns)</p>";
        } else {
            echo "<p>❌ <strong>Spreadsheet ID:</strong> Not configured</p>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>Google API Error:</strong> " . esc_html($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    // Test 4: Recommendations
    echo "<h2>4. 💡 Status & Recommendations</h2>";
    
    $issues = [];
    $recommendations = [];
    
    if (empty($settings['column_mapping_string'])) {
        $issues[] = "Column mapping string is empty";
        $recommendations[] = "Go to AccuFlow Settings → Update column mapping to match your sheet";
    }
    
    if (count($columns) < 14) {
        $issues[] = "Only " . count($columns) . " columns mapped (expected 14)";
        $recommendations[] = "Add missing column mappings for Platform, Plan, Price, Payment_Status, Notes";
    }
    
    // Check if all expected columns are mapped
    $missing_columns = [];
    foreach ($expected_structure as $expected_field) {
        $found = false;
        foreach ($columns as $field_name => $field_index) {
            if (strtolower($field_name) == strtolower($expected_field) || 
                str_replace('_', '', strtolower($field_name)) == str_replace('_', '', strtolower($expected_field))) {
                $found = true;
                break;
            }
        }
        if (!$found) {
            $missing_columns[] = $expected_field;
        }
    }
    
    if (!empty($missing_columns)) {
        $issues[] = "Missing columns: " . implode(', ', $missing_columns);
        $recommendations[] = "Add mappings for missing columns";
    }
    
    if (empty($issues)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>✅ Configuration Looks Good!</h3>";
        echo "<p>Your column mapping appears to be correctly configured for the new sheet structure.</p>";
        echo "<ul>";
        echo "<li>✅ All 14 columns are mapped</li>";
        echo "<li>✅ Column indices are correct</li>";
        echo "<li>✅ Google API range covers all columns (A:N)</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ Issues Found:</h3>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li>❌ {$issue}</li>";
        }
        echo "</ul>";
        echo "</div>";
        
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>🔧 Recommended Actions:</h3>";
        echo "<ol>";
        foreach ($recommendations as $rec) {
            echo "<li>{$rec}</li>";
        }
        echo "</ol>";
        echo "</div>";
    }
    
    // Quick fix button
    echo "<h2>5. 🚀 Quick Fix</h2>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>Click the button below to go to AccuFlow settings and apply the new column mapping:</p>";
    echo "<button onclick='goToSettings()' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin-right: 10px;'>🔧 Go to Settings</button>";
    echo "<button onclick='showInstructions()' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>📋 Show Instructions</button>";
    echo "</div>";
    
    echo "<div id='instructions' style='display: none; background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #007cba;'>";
    echo "<h3>📋 Manual Update Instructions:</h3>";
    echo "<ol>";
    echo "<li>Go to <strong>AccuFlow Settings</strong> → <strong>Column Configuration</strong></li>";
    echo "<li>Select <strong>\"Cấu hình theo chuỗi\"</strong></li>";
    echo "<li>Paste this string into the text area:</li>";
    echo "<code style='display: block; background: #e9ecef; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace;'>ID|Username|Password|Login_URL|Status|Order_ID|Sold_Date|Expiration_Date|Platform|Plan|Price|Payment_Status|Notes|Variation_Attribute</code>";
    echo "<li>Or click <strong>\"Reset to Default\"</strong> button</li>";
    echo "<li>Save settings</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Error:</strong> " . esc_html($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Column mapping test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<script>
function goToSettings() {
    var adminUrl = '<?php echo admin_url('admin.php?page=accuflow-sheets-integration'); ?>';
    window.open(adminUrl, '_blank');
}

function showInstructions() {
    var instructions = document.getElementById('instructions');
    if (instructions.style.display === 'none') {
        instructions.style.display = 'block';
    } else {
        instructions.style.display = 'none';
    }
}
</script>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
}

h1 { 
    color: #2271b1; 
    border-bottom: 3px solid #2271b1; 
    padding-bottom: 10px; 
}

h2 { 
    color: #135e96; 
    border-bottom: 2px solid #ddd; 
    padding-bottom: 5px; 
    margin-top: 30px; 
}

h3 { 
    color: #0073aa; 
    margin-top: 0;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background: #f0f0f0;
    font-weight: bold;
}

code {
    background: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
}
</style>
