<?php
/**
 * Test Connection và Log - Fix JSON response
 */

// Load WordPress
$wp_load_path = '../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('Cannot find WordPress. Please check path.');
}

// Security check
if (!current_user_can('manage_options')) {
    die('Permission denied. Please login as admin.');
}

// Set proper headers for JSON response
header('Content-Type: application/json; charset=utf-8');

echo "<h1>🔍 Test Connection & Log</h1>";
echo "<p><strong>Test time:</strong> " . date('Y-m-d H:i:s') . "</p>";

try {
    $config = new AccuFlow_Config();
    $utils = new AccuFlow_Utils();
    $google_api = new AccuFlow_Google_API($config, $utils);
    $settings = $config->get_config();
    
    if (empty($settings['spreadsheet_id'])) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<p>❌ Spreadsheet ID not configured</p>";
        echo "</div>";
        exit;
    }
    
    echo "<h2>📊 Connection Test</h2>";
    
    // Test Google Sheets service
    $service = $google_api->get_sheets_service();
    if (!$service) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<p>❌ Cannot connect to Google Sheets service</p>";
        echo "</div>";
        exit;
    }
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>✅ Google Sheets service connected successfully</p>";
    echo "</div>";
    
    // Get spreadsheet info
    $spreadsheet = $service->spreadsheets->get($settings['spreadsheet_id']);
    $sheets = $spreadsheet->getSheets();
    
    if (empty($sheets)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<p>❌ No sheets found in spreadsheet</p>";
        echo "</div>";
        exit;
    }
    
    $first_sheet = $sheets[0];
    $sheet_name = $first_sheet->getProperties()->getTitle();
    $sheet_gid = $first_sheet->getProperties()->getSheetId();
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Sheet found:</strong> {$sheet_name} (GID: {$sheet_gid})</p>";
    echo "</div>";
    
    // Test writing log to sheet
    echo "<h2>📝 Test Log Writing</h2>";
    
    // Create test tab if not exists
    $test_tab_name = "TEST CHỨC NĂNG";
    $test_tab_exists = false;
    
    foreach ($sheets as $sheet) {
        if ($sheet->getProperties()->getTitle() === $test_tab_name) {
            $test_tab_exists = true;
            break;
        }
    }
    
    if (!$test_tab_exists) {
        // Create test tab
        $requests = [
            new Google_Service_Sheets_Request([
                'addSheet' => [
                    'properties' => [
                        'title' => $test_tab_name
                    ]
                ]
            ])
        ];
        
        $batchUpdateRequest = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest([
            'requests' => $requests
        ]);
        
        $service->spreadsheets->batchUpdate($settings['spreadsheet_id'], $batchUpdateRequest);
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ Created test tab: {$test_tab_name}</p>";
        echo "</div>";
    }
    
    // Prepare log data exactly like the image format
    $log_headers = ['ID', 'Username', 'Password', 'Login_URL', 'Status', 'Order_ID', 'Sold_Date', 'Expiration_Date', 'Platform', 'Plan', 'Price', 'Payment_Status', 'Notes', 'Variation_Attribute'];
    
    $sample_log_data = [
        ['1', '<EMAIL>', 'password123', 'https://example.com/login', 'Available', '1001', '2024-01-15 10:30:00', '2025-01-31', 'WooCommerce', 'Premium Plan', '19.99', 'completed', 'Test log entry', 'Premium'],
        ['2', '<EMAIL>', 'password456', 'https://example.com/login', 'Sold', '1002', '2024-01-16 14:20:00', '2025-02-28', 'WooCommerce', 'Basic Plan', '9.99', 'completed', 'Sample data', 'Basic'],
        ['3', '<EMAIL>', 'password789', 'https://example.com/login', 'Available', '', '', '2025-03-31', 'WooCommerce', 'Premium Plan', '19.99', '', 'Ready for sale', 'Premium']
    ];
    
    // Write headers and sample data to test tab
    $range = $test_tab_name . '!A1:N' . (count($sample_log_data) + 1);
    $values = array_merge([$log_headers], $sample_log_data);
    
    $body = new Google_Service_Sheets_ValueRange([
        'values' => $values
    ]);
    
    $params = [
        'valueInputOption' => 'RAW'
    ];
    
    $result = $service->spreadsheets_values->update(
        $settings['spreadsheet_id'],
        $range,
        $body,
        $params
    );
    
    if ($result->getUpdatedCells() > 0) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ Successfully wrote log data to test tab</p>";
        echo "<p><strong>Updated cells:</strong> {$result->getUpdatedCells()}</p>";
        echo "</div>";
        
        // Format the header row (make it green like in the image)
        $header_format_requests = [
            new Google_Service_Sheets_Request([
                'repeatCell' => [
                    'range' => [
                        'sheetId' => null, // Will be set below
                        'startRowIndex' => 0,
                        'endRowIndex' => 1,
                        'startColumnIndex' => 0,
                        'endColumnIndex' => 14
                    ],
                    'cell' => [
                        'userEnteredFormat' => [
                            'backgroundColor' => [
                                'red' => 0.56,
                                'green' => 0.93,
                                'blue' => 0.56
                            ],
                            'textFormat' => [
                                'bold' => true
                            ]
                        ]
                    ],
                    'fields' => 'userEnteredFormat(backgroundColor,textFormat)'
                ]
            ])
        ];
        
        // Get the sheet ID for the test tab
        $updated_spreadsheet = $service->spreadsheets->get($settings['spreadsheet_id']);
        $updated_sheets = $updated_spreadsheet->getSheets();
        $test_sheet_id = null;
        
        foreach ($updated_sheets as $sheet) {
            if ($sheet->getProperties()->getTitle() === $test_tab_name) {
                $test_sheet_id = $sheet->getProperties()->getSheetId();
                break;
            }
        }
        
        if ($test_sheet_id !== null) {
            $header_format_requests[0]->getRepeatCell()->getRange()->setSheetId($test_sheet_id);
            
            $format_batch_request = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest([
                'requests' => $header_format_requests
            ]);
            
            $service->spreadsheets->batchUpdate($settings['spreadsheet_id'], $format_batch_request);
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>✅ Applied header formatting (green background)</p>";
            echo "</div>";
        }
        
        // Test reading the data back
        echo "<h2>📖 Test Reading Data Back</h2>";
        
        $read_range = $test_tab_name . '!A:N';
        $read_response = $service->spreadsheets_values->get($settings['spreadsheet_id'], $read_range);
        $read_values = $read_response->getValues();
        
        if (!empty($read_values)) {
            echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0; overflow-x: auto;'>";
            echo "<h4>📋 Data Successfully Written and Read Back:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
            
            foreach ($read_values as $row_index => $row) {
                $is_header = ($row_index === 0);
                $row_style = $is_header ? 'background: #90EE90; font-weight: bold;' : '';
                
                echo "<tr style='{$row_style}'>";
                for ($i = 0; $i < 14; $i++) {
                    $cell_value = isset($row[$i]) ? htmlspecialchars($row[$i]) : '';
                    echo "<td style='padding: 4px; border: 1px solid #ccc;'>{$cell_value}</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
        }
        
        // Return success JSON (fixing the Unicode issue)
        $success_message = "✅ Đã ghi log kết nối thành công vào tab \"{$test_tab_name}\".";
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>🎉 Test Completed Successfully!</h3>";
        echo "<p><strong>JSON Response:</strong></p>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace;'>";
        
        $json_response = [
            'success' => true,
            'data' => $success_message
        ];
        
        echo json_encode($json_response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        echo "</pre>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ Failed to write log data</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
    
    // Return error JSON
    $error_response = [
        'success' => false,
        'error' => $e->getMessage()
    ];
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Error JSON Response:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace;'>";
    echo json_encode($error_response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    echo "</pre>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Connection test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
}

h1 { 
    color: #2271b1; 
    border-bottom: 3px solid #2271b1; 
    padding-bottom: 10px; 
}

h2 { 
    color: #135e96; 
    border-bottom: 2px solid #ddd; 
    padding-bottom: 5px; 
    margin-top: 30px; 
}

h3, h4 { 
    color: #0073aa; 
    margin-top: 0;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background: #f0f0f0;
    font-weight: bold;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>
