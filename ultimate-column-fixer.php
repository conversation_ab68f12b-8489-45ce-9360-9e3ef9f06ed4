<?php
/**
 * Ultimate Column Fixer - <PERSON><PERSON><PERSON><PERSON> quyết tất cả vấn đề column mapping
 */

// Load WordPress
$wp_load_path = '../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('Cannot find WordPress. Please check path.');
}

// Security check
if (!current_user_can('manage_options')) {
    die('Permission denied. Please login as admin.');
}

echo "<h1>🔧 Ultimate Column Fixer</h1>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Handle actions
$action_result = '';

if ($_POST) {
    try {
        if (isset($_POST['force_reset'])) {
            // Force reset to clean A-N mapping
            $clean_mapping = [
                'ID' => 0, 'Username' => 1, 'Password' => 2, 'Login_URL' => 3,
                'Status' => 4, 'Order_ID' => 5, 'Sold_Date' => 6, 'Expiration_Date' => 7,
                'Platform' => 8, 'Plan' => 9, 'Price' => 10, 'Payment_Status' => 11,
                'Notes' => 12, 'Variation_Attribute' => 13
            ];
            
            // Clear all AccuFlow options first
            delete_option('accuflow_settings');
            
            // Set clean options
            $clean_options = [
                'columns' => $clean_mapping,
                'column_mapping_string' => 'ID|Username|Password|Login_URL|Status|Order_ID|Sold_Date|Expiration_Date|Platform|Plan|Price|Payment_Status|Notes|Variation_Attribute',
                'column_indices_string' => '0|1|2|3|4|5|6|7|8|9|10|11|12|13',
                'status_available' => 'Available',
                'status_sold' => 'Sold'
            ];
            
            update_option('accuflow_settings', $clean_options);
            $action_result = "✅ Force reset completed! Column mapping cleaned.";
            
        } elseif (isset($_POST['update_columns'])) {
            // Update specific columns
            $current_options = get_option('accuflow_settings', []);
            
            $new_mapping = [];
            $field_names = ['ID', 'Username', 'Password', 'Login_URL', 'Status', 'Order_ID', 'Sold_Date', 'Expiration_Date', 'Platform', 'Plan', 'Price', 'Payment_Status', 'Notes', 'Variation_Attribute'];
            
            foreach ($field_names as $field) {
                $input_name = 'col_' . strtolower(str_replace('_', '', $field));
                $value = isset($_POST[$input_name]) ? intval($_POST[$input_name]) : 0;
                $new_mapping[$field] = max(0, min(25, $value)); // Clamp between 0-25
            }
            
            $current_options['columns'] = $new_mapping;
            $current_options['column_mapping_string'] = implode('|', array_keys($new_mapping));
            $current_options['column_indices_string'] = implode('|', array_values($new_mapping));
            
            update_option('accuflow_settings', $current_options);
            $action_result = "✅ Column mapping updated successfully!";
        }
        
    } catch (Exception $e) {
        $action_result = "❌ Error: " . $e->getMessage();
    }
}

// Show action result
if (!empty($action_result)) {
    $bg_color = strpos($action_result, '✅') !== false ? '#d4edda' : '#f8d7da';
    echo "<div style='background: {$bg_color}; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>{$action_result}</h3>";
    echo "</div>";
}

// Get current config
try {
    $config = new AccuFlow_Config();
    $settings = $config->get_config();
    
    echo "<h2>🔍 Current Status Check</h2>";
    
    // Check for duplicate indices
    $indices = array_values($settings['columns']);
    $duplicates = array_diff_assoc($indices, array_unique($indices));
    
    if (!empty($duplicates)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ PROBLEM DETECTED: Duplicate Column Indices</h3>";
        echo "<p>Multiple fields are pointing to the same column. This is why it's not working!</p>";
        echo "<p><strong>Duplicate indices found:</strong> " . implode(', ', array_unique($duplicates)) . "</p>";
        echo "</div>";
        
        // Show force reset option
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>🔧 Quick Fix</h3>";
        echo "<form method='post'>";
        echo "<p><strong>Force reset to clean A-N mapping:</strong></p>";
        echo "<ul>";
        echo "<li>A=ID, B=Username, C=Password, D=Login_URL, E=Status...</li>";
        echo "<li>This will completely clean your configuration</li>";
        echo "</ul>";
        echo "<button type='submit' name='force_reset' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>🔄 Force Reset Now</button>";
        echo "</form>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>✅ Column mapping looks clean!</h3>";
        echo "</div>";
    }
    
    // Show current mapping
    echo "<h3>📋 Current Column Mapping</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #e9ecef; font-weight: bold;'>";
    echo "<th>Field</th><th>Column</th><th>Index</th><th>Status</th></tr>";
    
    foreach ($settings['columns'] as $field => $index) {
        $col_letter = chr(65 + $index);
        $status = '✅ OK';
        $row_color = '';
        
        // Check for issues
        if (array_count_values($indices)[$index] > 1) {
            $status = '❌ Duplicate';
            $row_color = 'background: #f8d7da;';
        } elseif ($field == 'Status') {
            $row_color = 'background: #d4edda; font-weight: bold;';
        }
        
        echo "<tr style='{$row_color}'>";
        echo "<td><strong>{$field}</strong></td>";
        echo "<td>{$col_letter}</td>";
        echo "<td>{$index}</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // Manual column editor
    echo "<h3>✏️ Manual Column Editor</h3>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<form method='post'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #e9ecef; font-weight: bold;'>";
    echo "<th style='width: 150px;'>Field</th><th style='width: 100px;'>Current</th><th style='width: 100px;'>New Column</th><th>Description</th></tr>";
    
    $descriptions = [
        'ID' => 'Account ID (1, 2, 3...)',
        'Username' => 'Login username/email',
        'Password' => 'Login password',
        'Login_URL' => 'Login page URL',
        'Status' => 'Available/Sold status',
        'Order_ID' => 'WooCommerce order ID',
        'Sold_Date' => 'Date when sold',
        'Expiration_Date' => 'Account expiry date',
        'Platform' => 'Platform name',
        'Plan' => 'Product/plan name',
        'Price' => 'Sale price',
        'Payment_Status' => 'Payment status',
        'Notes' => 'Additional notes',
        'Variation_Attribute' => 'Product variation'
    ];
    
    foreach ($settings['columns'] as $field => $index) {
        $col_letter = chr(65 + $index);
        $input_name = 'col_' . strtolower(str_replace('_', '', $field));
        $description = $descriptions[$field] ?? '';
        
        $row_style = '';
        if ($field == 'Status') $row_style = 'background: #d4edda;';
        
        echo "<tr style='{$row_style}'>";
        echo "<td><strong>{$field}</strong></td>";
        echo "<td>{$col_letter} ({$index})</td>";
        echo "<td><input type='number' name='{$input_name}' value='{$index}' min='0' max='25' style='width: 50px; padding: 3px;'></td>";
        echo "<td>{$description}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "<p style='margin-top: 15px;'>";
    echo "<button type='submit' name='update_columns' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>💾 Update Columns</button>";
    echo "</p>";
    echo "</form>";
    echo "</div>";
    
    // Test Google Sheets connection and show structured log
    echo "<h2>📊 Google Sheets Test & Structured Log</h2>";
    
    if (!empty($settings['spreadsheet_id'])) {
        $utils = new AccuFlow_Utils();
        $google_api = new AccuFlow_Google_API($config, $utils);
        $service = $google_api->get_sheets_service();
        
        if ($service) {
            try {
                $spreadsheet = $service->spreadsheets->get($settings['spreadsheet_id']);
                $sheets = $spreadsheet->getSheets();
                
                if (!empty($sheets)) {
                    $first_sheet = $sheets[0];
                    $sheet_name = $first_sheet->getProperties()->getTitle();
                    $sheet_gid = $first_sheet->getProperties()->getSheetId();
                    
                    // Read data
                    $range = $sheet_name . '!A:N';
                    $response = $service->spreadsheets_values->get($settings['spreadsheet_id'], $range);
                    $values = $response->getValues();
                    
                    if (!empty($values)) {
                        $header_row = isset($values[0]) ? $values[0] : [];
                        $data_rows = array_slice($values, 1);
                        
                        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; overflow-x: auto;'>";
                        echo "<h4>📋 Complete Structured Log Template</h4>";
                        echo "<p><strong>Sheet:</strong> {$sheet_name} | <strong>Total rows:</strong> " . count($data_rows) . "</p>";
                        
                        // Header template
                        echo "<h5>Header Structure:</h5>";
                        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 10px; margin-bottom: 15px;'>";
                        echo "<tr style='background: #e9ecef; font-weight: bold;'>";
                        $field_order = ['ID', 'Username', 'Password', 'Login_URL', 'Status', 'Order_ID', 'Sold_Date', 'Expiration_Date', 'Platform', 'Plan', 'Price', 'Payment_Status', 'Notes', 'Variation_Attribute'];
                        foreach ($field_order as $field) {
                            echo "<th style='padding: 5px; min-width: 80px;'>{$field}</th>";
                        }
                        echo "</tr>";
                        
                        // Header values
                        echo "<tr style='background: #fff3cd;'>";
                        foreach ($field_order as $field) {
                            $col_index = $settings['columns'][$field];
                            $header_value = isset($header_row[$col_index]) ? htmlspecialchars($header_row[$col_index]) : '[EMPTY]';
                            echo "<td style='padding: 5px;'>{$header_value}</td>";
                        }
                        echo "</tr>";
                        echo "</table>";
                        
                        // Data sample
                        echo "<h5>Data Sample (First 5 rows):</h5>";
                        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 10px;'>";
                        echo "<tr style='background: #e9ecef; font-weight: bold;'>";
                        echo "<th>Row</th>";
                        foreach ($field_order as $field) {
                            echo "<th style='padding: 5px; min-width: 80px;'>{$field}</th>";
                        }
                        echo "</tr>";
                        
                        $available_count = 0;
                        foreach (array_slice($data_rows, 0, 5) as $row_index => $row) {
                            $actual_row = $row_index + 2;
                            echo "<tr>";
                            echo "<td style='padding: 3px; font-weight: bold;'>{$actual_row}</td>";
                            
                            foreach ($field_order as $field) {
                                $col_index = $settings['columns'][$field];
                                $cell_value = isset($row[$col_index]) ? htmlspecialchars($row[$col_index]) : '';
                                
                                $bg_color = '';
                                if ($field == 'Status') {
                                    if (trim($cell_value) === $settings['status_available']) {
                                        $bg_color = 'background: #d4edda; font-weight: bold;';
                                        $available_count++;
                                    } elseif (trim($cell_value) === $settings['status_sold']) {
                                        $bg_color = 'background: #f8d7da; font-weight: bold;';
                                    } else {
                                        $bg_color = 'background: #fff3cd; font-weight: bold;';
                                    }
                                }
                                
                                echo "<td style='padding: 3px; {$bg_color}'>{$cell_value}</td>";
                            }
                            echo "</tr>";
                        }
                        echo "</table>";
                        echo "</div>";
                        
                        // Test account finding
                        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4>🧪 Available Account Test</h4>";
                        
                        $test_account = $google_api->find_first_available_account_in_gid($sheet_gid);
                        
                        if ($test_account) {
                            echo "<p>✅ <strong>SUCCESS!</strong> Found available account:</p>";
                            echo "<ul>";
                            echo "<li><strong>Row:</strong> {$test_account['row_number']}</li>";
                            echo "<li><strong>ID:</strong> " . htmlspecialchars($test_account['id']) . "</li>";
                            echo "<li><strong>Username:</strong> " . htmlspecialchars($test_account['username']) . "</li>";
                            echo "<li><strong>Password:</strong> " . htmlspecialchars($test_account['password']) . "</li>";
                            echo "<li><strong>Login URL:</strong> " . htmlspecialchars($test_account['login_url']) . "</li>";
                            echo "</ul>";
                        } else {
                            echo "<p>❌ <strong>FAILED!</strong> No available account found.</p>";
                            echo "<p><strong>Possible issues:</strong></p>";
                            echo "<ul>";
                            echo "<li>Status column not reading from correct column</li>";
                            echo "<li>No rows with Status = '{$settings['status_available']}'</li>";
                            echo "<li>Case sensitivity issue</li>";
                            echo "</ul>";
                        }
                        echo "</div>";
                    }
                }
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
                echo "<p>❌ Google Sheets Error: " . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<p>❌ <strong>Configuration Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Ultimate fixer completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
}

h1 { 
    color: #2271b1; 
    border-bottom: 3px solid #2271b1; 
    padding-bottom: 10px; 
}

h2 { 
    color: #135e96; 
    border-bottom: 2px solid #ddd; 
    padding-bottom: 5px; 
    margin-top: 30px; 
}

h3, h4, h5 { 
    color: #0073aa; 
    margin-top: 0;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    text-align: left;
    border: 1px solid #ddd;
    padding: 5px;
}

th {
    background: #f0f0f0;
    font-weight: bold;
}

button {
    cursor: pointer;
    transition: all 0.3s;
}

button:hover {
    opacity: 0.9;
}

input[type="number"] {
    border: 1px solid #ddd;
    border-radius: 3px;
}
</style>
